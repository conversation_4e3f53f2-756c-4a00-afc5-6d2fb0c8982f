<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>IllusionChess</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" type="text/css" media="screen" href="style.css">
    <link rel="stylesheet" href="css/chessboard.min.css" type="text/css" />
</head>
<body>
    <div class="container">
        <div id="top-controls">
            <div id="main-buttons">
                <button id="human">Human</button>
                <div id="robot-section">
                    <span id="robot-label">Robot:</span>
                    <button id="easy">Easy</button>
                    <button id="inter">Medium</button>
                    <button id="hard">Hard</button>
                </div>
            </div>
            <div id="clear-section">
                <button id="reset">Clear</button>
            </div>
        </div>

        <p id="log"></p>
        <div id="chessboard" style="width: 1064px"></div>
        <div id="last-move">Ready to play</div>
    </div>
    
    <script src="js/polyfill.min.js"></script>
    <script src="js/jquery.js"></script> <!-- Transpiled To ES5 With Babel -->
    <script src="js/chessboard.min.js"></script> <!-- Transpiled To ES5 With Babel -->
    <script src="js/chess.js"></script> <!-- Transpiled To ES5 With Babel -->
    <script src="script.js?v=0.4"></script> <!-- Transpiled To ES5 With Babel -->
    <!-- Stockfish Doesn't Work, JS-CHESS-ENGINE Has F'ed Up UMD... -->
    <!-- Note: 404 error for /.well-known/appspecific/com.chrome.devtools.json is normal Chrome DevTools behavior -->
</body>
</html>