"use strict";

if (!localStorage.getItem("reloaded")) {
    localStorage.setItem("reloaded", "true");
    window.location.reload();
} else {
    localStorage.removeItem("reloaded");
}; //(Hopefully) Fix Scaling Issues On Elder Kindles

var selectedsquare = null;
var difficultysettings = {
    easy: { skill: 1 },
    inter: { skill: 2 },
    hard: { skill: 3 }
};

var game = new Chess();
var board = Chessboard("chessboard", {
    draggable: false,
    position: "start",
    moveSpeed: 0, // No animation
    snapbackSpeed: 0, // No snapback animation
    snapSpeed: 0, // No snap animation
    appearSpeed: 0, // No appear animation
    trashSpeed: 0, // No removal animation
    onSnapEnd: function onSnapEnd() {
        return board.position(game.fen());
    }
});

var mode = "ai";

// Initialize button states
$(document).ready(function() {

    $("#easy").addClass("selected"); // Default to easy difficulty (AI mode)
    // Human button starts unselected, difficulty buttons enabled by default

    // Force disable all jQuery animations globally
    $.fx.off = true;

    // Override jQuery animate function to make it instant
    var originalAnimate = $.fn.animate;
    $.fn.animate = function(properties, duration, easing, complete) {
        // Call the original animate but with 0 duration
        return originalAnimate.call(this, properties, 0, easing, complete);
    };

    // Override any CSS transitions on chessboard elements
    $('#chessboard').find('*').css({
        'transition': 'none',
        'animation': 'none',
        '-webkit-transition': 'none',
        '-webkit-animation': 'none'
    });

    // Set up MutationObserver to disable animations on any new elements
    var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                $(mutation.addedNodes).find('*').addBack().css({
                    'transition': 'none !important',
                    'animation': 'none !important',
                    '-webkit-transition': 'none !important',
                    '-webkit-animation': 'none !important'
                });
            }
        });
    });

    // Start observing the chessboard
    var chessboardElement = document.getElementById('chessboard');
    if (chessboardElement) {
        observer.observe(chessboardElement, {
            childList: true,
            subtree: true
        });
    }

    console.log("=== CHESS GAME INITIALIZED ===");
    console.log("VERSION=0.2");
    console.log("Script loaded successfully!");
    console.log("Initialized: mode =", mode, "difficulty = easy, animations disabled");
    console.log("Screen width:", window.innerWidth, "Screen height:", window.innerHeight);
    console.log("=== READY TO PLAY ===");
});

// Game mode button handlers
$("#human").on("click", function () {
    console.log("Human mode button clicked");
    mode = "human";
    $("#human").addClass("selected");
    $("#easy, #inter, #hard").removeClass("selected");
    console.log("Mode changed to: human");
    resetgame();
});

// Difficulty button handlers - these also switch to AI mode
$("#easy, #inter, #hard").on("click", function () {
    var diffId = this.id;
    console.log("Difficulty button clicked:", diffId);

    if (difficultysettings[diffId]) {
        // Switch to AI mode
        mode = "ai";
        $("#human").removeClass("selected");
        $("#easy, #inter, #hard").removeClass("selected");
        $(this).addClass("selected");
        console.log("Mode changed to: ai, difficulty:", diffId);
        resetgame();
    }
});

// Function to display last move
function displayLastMove(move, playerType) {
    var moveText = "";
    if (move) {
        var from = move.from ? move.from.toUpperCase() : "";
        var to = move.to ? move.to.toUpperCase() : "";
        var piece = move.piece ? move.piece.toUpperCase() : "";
        var captured = move.captured ? " captures " + move.captured.toUpperCase() : "";

        // Determine player name based on mode and move color
        var playerName = "";
        if (mode === "human") {
            // In human vs human mode, determine by piece color
            if (move.color === "w") {
                playerName = "Player 1 (White)";
            } else {
                playerName = "Player 2 (Black)";
            }
        } else {
            // In AI mode, use the provided playerType
            playerName = playerType;
        }

        moveText = playerName + ": " + piece + " " + from + " → " + to + captured;
    } else {
        moveText = playerType + " move";
    }

    document.getElementById("last-move").textContent = moveText;
    console.log("Last move displayed:", moveText);
}

// Function to instantly update board position without any animation
function updateBoardInstantly(fen) {
    // Force immediate position update
    board.position(fen, false);

    // Additional safety: remove any ongoing animations
    $('#chessboard').find('*').stop(true, true);
    $('#chessboard').find('*').css({
        'transition': 'none',
        'animation': 'none',
        'transform': '',
        'left': '',
        'top': ''
    });
}

function resetgame() {
    game.reset();
    updateBoardInstantly("start");
    removegreysquares();
    selectedsquare = null;

    // Reset last move display and remove game-over styling
    var lastMoveElement = document.getElementById("last-move");
    lastMoveElement.textContent = "Game reset - Ready to play";
    lastMoveElement.classList.remove("game-over");
}

var whitesquaregrey = "#a9a9a9";
var blacksquaregrey = "#696969";

function removegreysquares() {
    $("#chessboard .square-55d63").css("outline", "").css("outline-offset", "");
}

function greysquare(sq) {
    var s = $("#chessboard .square-" + sq);
    s.css({
        outline: "5px dashed black",
        "outline-offset": "-5px"
    });
}

var pawnevalwhite = [[0, 0, 0, 0, 0, 0, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5], [1, 1, 2, 3, 3, 2, 1, 1], [0.5, 0.5, 1, 2.5, 2.5, 1, 0.5, 0.5], [0, 0, 0, 2, 2, 0, 0, 0], [0.5, -0.5, -1, 0, 0, -1, -0.5, 0.5], [0.5, 1, 1, -2, -2, 1, 1, 0.5], [0, 0, 0, 0, 0, 0, 0, 0]];
var pawnevalblack = pawnevalwhite.slice().reverse();
var knighteval = [[-5, -4, -3, -3, -3, -3, -4, -5], [-4, -2, 0, 0, 0, 0, -2, -4], [-3, 0, 1, 1.5, 1.5, 1, 0, -3], [-3, 0.5, 1.5, 2, 2, 1.5, 0.5, -3], [-3, 0, 1.5, 2, 2, 1.5, 0, -3], [-3, 0.5, 1, 1.5, 1.5, 1, 0.5, -3], [-4, -2, 0, 0.5, 0.5, 0, -2, -4], [-5, -4, -3, -3, -3, -3, -4, -5]];
var bishopevalwhite = [[-2, -1, -1, -1, -1, -1, -1, -2], [-1, 0, 0, 0, 0, 0, 0, -1], [-1, 0, 0.5, 1, 1, 0.5, 0, -1], [-1, 0.5, 0.5, 1, 1, 0.5, 0.5, -1], [-1, 0, 1, 1, 1, 1, 0, -1], [-1, 1, 1, 1, 1, 1, 1, -1], [-1, 0.5, 0, 0, 0, 0, 0.5, -1], [-2, -1, -1, -1, -1, -1, -1, -2]];
var bishopevalblack = bishopevalwhite.slice().reverse();
var rookevalwhite = [[0, 0, 0, 0, 0, 0, 0, 0], [0.5, 1, 1, 1, 1, 1, 1, 0.5], [-0.5, 0, 0, 0, 0, 0, 0, -0.5], [-0.5, 0, 0, 0, 0, 0, 0, -0.5], [-0.5, 0, 0, 0, 0, 0, 0, -0.5], [-0.5, 0, 0, 0, 0, 0, 0, -0.5], [-0.5, 0, 0, 0, 0, 0, 0, -0.5], [0, 0, 0, 0.5, 0.5, 0, 0, 0]];
var rookevalblack = rookevalwhite.slice().reverse();
var evalqueen = [[-2, -1, -1, -0.5, -0.5, -1, -1, -2], [-1, 0, 0, 0, 0, 0, 0, -1], [-1, 0, 0.5, 0.5, 0.5, 0.5, 0, -1], [-0.5, 0, 0.5, 0.5, 0.5, 0.5, 0, -0.5], [0, 0, 0.5, 0.5, 0.5, 0.5, 0, -0.5], [-1, 0.5, 0.5, 0.5, 0.5, 0.5, 0, -1], [-1, 0, 0.5, 0, 0, 0, 0, -1], [-2, -1, -1, -0.5, -0.5, -1, -1, -2]];
var kingevalwhite = [[-3, -4, -4, -5, -5, -4, -4, -3], [-3, -4, -4, -5, -5, -4, -4, -3], [-3, -4, -4, -5, -5, -4, -4, -3], [-3, -4, -4, -5, -5, -4, -4, -3], [-2, -3, -3, -4, -4, -3, -3, -2], [-1, -2, -2, -2, -2, -2, -2, -1], [2, 2, 0, 0, 0, 0, 2, 2], [2, 3, 1, 0, 0, 1, 3, 2]];
var kingevalblack = kingevalwhite.slice().reverse();

function getpiecevalue(p, x, y) {
    if (!p) return 0;
    var w = p.color === "w";
    var b = void 0;
    switch (p.type) {
        case "p":
            b = 10 + (w ? pawnevalwhite[y][x] : pawnevalblack[y][x]);break;
        case "n":
            b = 30 + knighteval[y][x];break;
        case "b":
            b = 30 + (w ? bishopevalwhite[y][x] : bishopevalblack[y][x]);break;
        case "r":
            b = 50 + (w ? rookevalwhite[y][x] : rookevalblack[y][x]);break;
        case "q":
            b = 90 + evalqueen[y][x];break;
        case "k":
            b = 900 + (w ? kingevalwhite[y][x] : kingevalblack[y][x]);break;
        default:
            return 0;
    }
    return w ? b : -b;
}

var positioncount = 0;

function minimaxroot(d, g, max) {
    var m = g.moves({ verbose: true });
    var bv = -Infinity,
        bm = null;
    var _iteratorNormalCompletion = true;
    var _didIteratorError = false;
    var _iteratorError = undefined;

    try {
        for (var _iterator = m[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {
            var mm = _step.value;

            g.move(mm);
            var v = minimax(d - 1, g, -1e4, 1e4, !max);
            g.undo();
            if (v > bv) {
                bv = v;
                bm = mm;
            }
        }
    } catch (err) {
        _didIteratorError = true;
        _iteratorError = err;
    } finally {
        try {
            if (!_iteratorNormalCompletion && _iterator.return) {
                _iterator.return();
            }
        } finally {
            if (_didIteratorError) {
                throw _iteratorError;
            }
        }
    }

    return bm;
}

function minimax(d, g, a, b, max) {
    positioncount++;
    if (d === 0) return -evaluateboard(g.board());
    var m = g.moves({ verbose: true });
    if (max) {
        var best = -Infinity;
        var _iteratorNormalCompletion2 = true;
        var _didIteratorError2 = false;
        var _iteratorError2 = undefined;

        try {
            for (var _iterator2 = m[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {
                var mm = _step2.value;

                g.move(mm);
                best = Math.max(best, minimax(d - 1, g, a, b, false));
                g.undo();
                a = Math.max(a, best);
                if (b <= a) break;
            }
        } catch (err) {
            _didIteratorError2 = true;
            _iteratorError2 = err;
        } finally {
            try {
                if (!_iteratorNormalCompletion2 && _iterator2.return) {
                    _iterator2.return();
                }
            } finally {
                if (_didIteratorError2) {
                    throw _iteratorError2;
                }
            }
        }

        return best;
    } else {
        var _best = Infinity;
        var _iteratorNormalCompletion3 = true;
        var _didIteratorError3 = false;
        var _iteratorError3 = undefined;

        try {
            for (var _iterator3 = m[Symbol.iterator](), _step3; !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done); _iteratorNormalCompletion3 = true) {
                var _mm = _step3.value;

                g.move(_mm);
                _best = Math.min(_best, minimax(d - 1, g, a, b, true));
                g.undo();
                b = Math.min(b, _best);
                if (b <= a) break;
            }
        } catch (err) {
            _didIteratorError3 = true;
            _iteratorError3 = err;
        } finally {
            try {
                if (!_iteratorNormalCompletion3 && _iterator3.return) {
                    _iterator3.return();
                }
            } finally {
                if (_didIteratorError3) {
                    throw _iteratorError3;
                }
            }
        }

        return _best;
    }
}

function evaluateboard(bd) {
    var t = 0;
    for (var y = 0; y < 8; y++) {
        for (var x = 0; x < 8; x++) {
            t += getpiecevalue(bd[y][x], x, y);
        }
    }return t;
}

$("#chessboard").on("click", ".square-55d63", function () {
    var sq = $(this).attr("data-square");
    var pc = game.get(sq);
    var tc = game.turn();
    var ip = pc && pc.color === tc;
    var lm = game.moves({ square: sq, verbose: true });
    if (!selectedsquare) {
        if (!ip || lm.length === 0) return;
        selectedsquare = sq;
        removegreysquares();
        greysquare(sq);
        lm.forEach(function (m) {
            return greysquare(m.to);
        });
        return;
    }
    if (selectedsquare === sq) {
        removegreysquares();
        selectedsquare = null;
        return;
    }
    if (ip) {
        if (lm.length === 0) {
            removegreysquares();
            selectedsquare = null;
            return;
        }
        selectedsquare = sq;
        removegreysquares();
        greysquare(sq);
        lm.forEach(function (m) {
            return greysquare(m.to);
        });
        return;
    }
    var mv = game.move({ from: selectedsquare, to: sq, promotion: "q" });
    removegreysquares();
    if (!mv) {
        greysquare(selectedsquare);
        game.moves({ square: selectedsquare, verbose: true }).forEach(function (m) {
            return greysquare(m.to);
        });
        return;
    }
    updateBoardInstantly(game.fen());
    selectedsquare = null;

    // Display player's move (function will determine player name based on mode)
    displayLastMove(mv, "Player");

    handlegameover();
    console.log("Move made. Current mode:", mode, "Game over:", game.game_over());
    if (mode === "ai" && !game.game_over()) {
        console.log("AI turn starting...");
        setTimeout(function () {
            var difficultyId = $("#easy.selected, #inter.selected, #hard.selected").attr("id") || "easy";
            var depth = difficultysettings[difficultyId].skill;
            console.log("AI difficulty:", difficultyId, "depth:", depth);
            var bm = minimaxroot(depth, game, true);
            var aiMove = game.move(bm);
            updateBoardInstantly(game.fen());

            // Display AI's move
            displayLastMove(aiMove, "Computer");

            console.log("AI move completed");
            handlegameover();
        }, 250);
    } else if (mode === "human") {
        console.log("Human vs Human mode - no AI move");
    }
});

$("#chessboard").parent().on("click", function (e) {
    if (!$(e.target).closest(".square-55d63").length) {
        removegreysquares();
        selectedsquare = null;
    }
});

function handlegameover() {
    if (!game.game_over()) return;

    var winner = "";
    if (game.in_checkmate()) {
        // If it's white's turn and checkmate, white lost (black wins)
        // If it's black's turn and checkmate, black lost (white wins)
        winner = (game.turn() === "w" ? "Black" : "White") + " Wins!";
    } else if (game.in_stalemate()) {
        winner = "Draw! (Stalemate)";
    } else if (game.in_threefold_repetition()) {
        winner = "Draw! (Threefold Repetition)";
    } else if (game.insufficient_material()) {
        winner = "Draw! (Insufficient Material)";
    } else {
        winner = "Draw!";
    }

    console.log("Game over:", winner, "Current turn:", game.turn(), "Checkmate:", game.in_checkmate());

    // Display game result in the last move area with special styling
    var lastMoveElement = document.getElementById("last-move");
    lastMoveElement.textContent = "🎉 " + winner + " 🎉";
    lastMoveElement.classList.add("game-over");

    // No automatic reset - user must click Clear button to start new game
}

$("#reset").on("click", resetgame);
