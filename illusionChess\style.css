/* Global animation disable */
*, *::before, *::after {
    animation-duration: 0s !important;
    animation-delay: 0s !important;
    transition-duration: 0s !important;
    transition-delay: 0s !important;
}

@font-face {
    font-family: "Libre Baskerville";
    src: url(assets/libre-baskerville.ttf);
}
  
html, body {
    margin: 0;
    padding: 0;
    font-family: "Libre Baskerville", "Georgia", serif;
    background-color: #ffffff;
    height: 100%;
    width: 100%;
    overflow-x: hidden;
}

h1 {
    margin-bottom: 2px;
}

.container {
    text-align: center;
    padding-top: 15px; /* Increased padding for taller buttons */
}

#chessboard {
    margin-left: auto;
    margin-right: auto;
    display: block;
}

/* Completely disable all animations and transitions */
#chessboard *,
#chessboard *::before,
#chessboard *::after,
.board-b72b1 *,
.square-55d63 *,
[class*="piece-"] {
    transition: none !important;
    animation: none !important;
    -webkit-transition: none !important;
    -webkit-animation: none !important;
    -moz-transition: none !important;
    -moz-animation: none !important;
    -o-transition: none !important;
    -o-animation: none !important;
    -ms-transition: none !important;
    -ms-animation: none !important;
    transition-duration: 0s !important;
    animation-duration: 0s !important;
    -webkit-transition-duration: 0s !important;
    -webkit-animation-duration: 0s !important;
}

/* Increase font size and make bold for board notation (letters and numbers) */
.notation-322f9 {
    font-size: 24px !important; /* Increased from 14px */
    font-weight: bold !important;
    font-family: "Libre Baskerville", "Georgia", serif !important;
}

/* Specific styles for letters (a-h) and numbers (1-8) */
.alpha-d2270,
.numeric-fc462 {
    font-size: 24px !important;
    font-weight: bold !important;
    font-family: "Libre Baskerville", "Georgia", serif !important;
}

button { /* Optimized for Kindle PW5 1236x1648 */
    font-family: "Libre Baskerville", "Georgia", serif;
    font-size: 27px; /* Increased by 1/3: 20 * 1.33 ≈ 27 */
    text-align: center;
    margin: 5px;
    height: 47px; /* Increased by 1/3: 35 * 1.33 ≈ 47 */
    width: auto;
    max-width: 200px; /* Prevent buttons from being too wide */
    white-space: nowrap; /* Prevent text wrapping */
    overflow: hidden; /* Hide overflow text */
}

button.selected {
    background-color: #4CAF50;
    color: white;
    border: 2px solid #45a049;
}

button.disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
    opacity: 0.6;
}

#last-move {
    font-size: 24px; /* Increased by 1/3: 18 * 1.33 ≈ 24 */
    font-weight: bold;
    color: #333;
    height: 47px; /* Same height as buttons */
    line-height: 47px; /* Center text vertically */
    margin: 10px 0;
    padding: 0 43px; /* Same padding as controls */
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Special styling for game over messages */
#last-move.game-over {
    background-color: #4CAF50 !important;
    color: white !important;
    border-color: #45a049 !important;
    animation: celebration 0.5s ease-in-out;
    font-weight: bold;
}

@keyframes celebration {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

#top-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 43px; /* Same padding as chessboard: (1236-1150)/2 = 43px */
}

#main-buttons {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: nowrap; /* Force single row */
    overflow-x: auto; /* Allow horizontal scroll if needed */
}

#robot-section {
    display: flex;
    gap: 5px;
    align-items: center;
}

#robot-label {
    font-family: "Libre Baskerville", "Georgia", serif;
    font-size: 27px; /* Same as button font size */
    font-weight: bold;
    color: #333;
    margin-right: 5px;
}

#clear-section {
    margin-left: 15px;
}

#reset {
    background-color: #f44336;
    color: white;
    border: none;
    border-radius: 4px;
}

#reset:hover {
    background-color: #d32f2f;
}

.move-dot {
    width: 20px;
    height: 20px;
    border: 2px dashed black;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    box-sizing: border-box;
    background: none;
}

/* Specific optimization for 1236px width screens */
@media (max-width: 1250px) and (min-width: 1200px) {
    button {
        font-size: 18px !important; /* Smaller font to fit */
        height: 47px;
        margin: 2px !important;
        padding: 0 6px !important;
        max-width: 150px;
    }

    #main-buttons {
        gap: 8px !important;
    }

    #robot-section {
        gap: 3px !important;
    }

    #robot-label {
        font-size: 18px !important;
        margin-right: 3px;
    }

    #top-controls {
        padding: 0 15px !important;
    }
}

/* Optimization for Kindle PW5 1236x1648 screen */
@media (min-width: 1101px) and (max-width: 1300px) {
    button {
        font-size: 27px; /* Increased by 1/3: 20 * 1.33 ≈ 27 */
        height: 47px; /* Increased by 1/3: 35 * 1.33 ≈ 47 */
        margin: 2px; /* Slightly reduced margins */
        padding: 0 8px; /* Slightly reduced horizontal padding */
        min-width: auto; /* Allow buttons to be as narrow as needed */
    }

    #chessboard {
        width: 1064px !important; /* Width minus padding for Kindle PW5 */
    }

    #top-controls {
        padding: 0 20px; /* Reduced padding to give more space for buttons */
        margin-bottom: 15px;
    }

    #main-buttons {
        gap: 5px; /* Reduced gap between button groups */
    }

    #robot-section {
        gap: 2px; /* Minimal gap between robot buttons */
    }

    #robot-label {
        font-size: 27px; /* Same as button font for PW5 */
        margin-right: 3px;
    }

    #clear-section {
        margin-left: 8px; /* Reduced margin for clear button */
    }

    .container {
        padding-top: 15px; /* Increased padding for taller buttons */
    }

    /* Even larger notation for Kindle PW5 screen */
    .notation-322f9,
    .alpha-d2270,
    .numeric-fc462 {
        font-size: 28px !important; /* Even larger for Kindle */
        font-weight: bold !important;
    }
}

/* Optimization for Kindle Gen 6 1024x768 screen */
@media (max-width: 1100px) {
    button {
        font-size: 12px; /* Smaller font for narrow screen */
        height: 35px; /* Reduced height to save vertical space */
        margin: 1px; /* Minimal margins */
        padding: 0 4px; /* Minimal horizontal padding */
        min-width: auto; /* Allow buttons to be as narrow as needed */
    }

    #chessboard {
        width: 650px !important; /* Much smaller board for Gen 6 */
    }

    #top-controls {
        padding: 0 10px; /* Minimal padding */
        margin-bottom: 5px; /* Reduced margin */
    }

    #main-buttons {
        gap: 3px; /* Minimal gap between button groups */
    }

    #robot-section {
        gap: 1px; /* Minimal gap between robot buttons */
    }

    #robot-label {
        font-size: 12px; /* Smaller for Gen 6 */
        margin-right: 2px;
    }

    #clear-section {
        margin-left: 5px; /* Minimal margin for clear button */
    }

    #last-move {
        font-size: 16px; /* Smaller font for last move */
        height: 35px; /* Match button height */
        line-height: 35px; /* Center text vertically */
        padding: 0 10px; /* Reduced padding */
        margin: 5px 0; /* Reduced margins */
    }

    .container {
        padding-top: 2px; /* Minimal padding for Gen 6 */
    }

    #log {
        margin: 5px 0; /* Reduced margins for log */
        min-height: 20px; /* Smaller min height */
        padding: 3px; /* Reduced padding */
    }

    /* Smaller notation for Gen 6 Kindle */
    .notation-322f9,
    .alpha-d2270,
    .numeric-fc462 {
        font-size: 18px !important; /* Even smaller for Gen 6 */
        font-weight: bold !important;
    }
}

/* Extra optimization for very narrow screens (Kindle Gen 6 portrait or smaller) */
@media (max-width: 800px) {
    button {
        font-size: 10px; /* Very small font */
        height: 30px; /* Very compact height */
        margin: 0px; /* No margins */
        padding: 0 2px; /* Minimal padding */
    }

    #chessboard {
        width: 500px !important; /* Very small board for portrait */
    }

    #top-controls {
        padding: 0 5px; /* Minimal padding */
        margin-bottom: 3px; /* Minimal margin */
    }

    #main-buttons {
        gap: 2px;
    }

    #robot-section {
        gap: 1px;
    }

    #robot-label {
        font-size: 10px; /* Very small for portrait */
        margin-right: 1px;
    }

    #clear-section {
        margin-left: 3px;
    }

    #last-move {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
        padding: 0 5px;
        margin: 3px 0; /* Minimal margins */
    }

    .container {
        padding-top: 1px; /* Minimal padding */
    }

    #log {
        margin: 3px 0; /* Minimal margins */
        min-height: 15px; /* Very small min height */
        padding: 2px; /* Minimal padding */
        font-size: 14px; /* Smaller font */
    }

    /* Very small notation for portrait */
    .notation-322f9,
    .alpha-d2270,
    .numeric-fc462 {
        font-size: 14px !important; /* Very small for portrait */
        font-weight: bold !important;
    }
}
