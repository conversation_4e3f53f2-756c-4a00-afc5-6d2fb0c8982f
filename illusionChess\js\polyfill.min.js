/*
 * Polyfill service v3.111.0
 * Disable minification (remove `.min` from URL path) for more info
*/

(function(self, undefined) {!function(t){"use strict";function e(t){switch(typeof t){case"undefined":return"undefined";case"boolean":return"boolean";case"number":return"number";case"string":return"string";default:return null===t?"null":"object"}}function r(t){return Object.prototype.toString.call(t).replace(/^\[object *|\]$/g,"")}function n(t){return"function"==typeof t}function o(t){if(null===t||t===B)throw TypeError();return Object(t)}function i(t){return t>>0}function f(t){return t>>>0}function u(e){if(!("TYPED_ARRAY_POLYFILL_NO_ARRAY_ACCESSORS"in t)){if(e.length>N)throw RangeError("Array too large for polyfill");var r;for(r=0;r<e.length;r+=1)!function n(t){try{Object.defineProperty(e,t,{get:function(){return e._getter(t)},set:function(r){e._setter(t,r)},enumerable:!0,configurable:!1})}catch(r){}}(r)}}function a(t,e){var r=32-e;return t<<r>>r}function h(t,e){var r=32-e;return t<<r>>>r}function y(t){return[255&t]}function s(t){return a(t[0],8)}function l(t){return[255&t]}function p(t){return h(t[0],8)}function c(t){return t=x(Number(t)),[t<0?0:t>255?255:255&t]}function b(t){return[255&t,t>>8&255]}function g(t){return a(t[1]<<8|t[0],16)}function E(t){return[255&t,t>>8&255]}function v(t){return h(t[1]<<8|t[0],16)}function _(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]}function O(t){return a(t[3]<<24|t[2]<<16|t[1]<<8|t[0],32)}function d(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]}function j(t){return h(t[3]<<24|t[2]<<16|t[1]<<8|t[0],32)}function P(t,e,r){function n(t){var e=m(t),r=t-e;return r<.5?e:r>.5?e+1:e%2?e+1:e}var o,i,f,u=(1<<e-1)-1;if(t!==t)i=(1<<e)-1,f=F(2,r-1),o=0;else if(t===Infinity||t===-Infinity)i=(1<<e)-1,f=0,o=t<0?1:0;else if(0===t)i=0,f=0,o=1/t==-Infinity?1:0;else if(o=t<0,(t=M(t))>=F(2,1-u)){i=Y(m(I(t)/S),1023);var a=t/F(2,i);a<1&&(i-=1,a*=2),a>=2&&(i+=1,a/=2);var h=F(2,r);f=n(a*h)-h,i+=u,f/h>=1&&(i+=1,f=0),i>2*u&&(i=(1<<e)-1,f=0)}else i=0,f=n(t/F(2,1-u-r));var y,s=[];for(y=r;y;y-=1)s.push(f%2?1:0),f=m(f/2);for(y=e;y;y-=1)s.push(i%2?1:0),i=m(i/2);s.push(o?1:0),s.reverse();for(var l=s.join(""),p=[];l.length;)p.unshift(parseInt(l.substring(0,8),2)),l=l.substring(8);return p}function T(t,e,r){var n,o,i,f,u,a,h,y,s=[];for(n=0;n<t.length;++n)for(i=t[n],o=8;o;o-=1)s.push(i%2?1:0),i>>=1;return s.reverse(),f=s.join(""),u=(1<<e-1)-1,a=parseInt(f.substring(0,1),2)?-1:1,h=parseInt(f.substring(1,1+e),2),y=parseInt(f.substring(1+e),2),h===(1<<e)-1?0!==y?NaN:a*Infinity:h>0?a*F(2,h-u)*(1+y/F(2,r)):0!==y?a*F(2,-(u-1))*(y/F(2,r)):a<0?-0:0}function w(t){return T(t,11,52)}function A(t){return P(t,11,52)}function L(t){return T(t,8,23)}function R(t){return P(t,8,23)}var B=void 0,N=1e5,S=Math.LN2,M=Math.abs,m=Math.floor,I=Math.log,U=Math.max,Y=Math.min,F=Math.pow,x=Math.round;!function(){var t=Object.defineProperty,e=!function(){try{return Object.defineProperty({},"x",{})}catch(t){return!1}}();t&&!e||(Object.defineProperty=function(e,r,n){if(t)try{return t(e,r,n)}catch(o){}if(e!==Object(e))throw TypeError("Object.defineProperty called on non-object");return Object.prototype.__defineGetter__&&"get"in n&&Object.prototype.__defineGetter__.call(e,r,n.get),Object.prototype.__defineSetter__&&"set"in n&&Object.prototype.__defineSetter__.call(e,r,n.set),"value"in n&&(e[r]=n.value),e})}(),function(){function a(t){if((t=i(t))<0)throw RangeError("ArrayBuffer size is not a small enough positive integer.");Object.defineProperty(this,"byteLength",{value:t}),Object.defineProperty(this,"_bytes",{value:Array(t)});for(var e=0;e<t;e+=1)this._bytes[e]=0}function h(){if(!arguments.length||"object"!=typeof arguments[0])return function(t){if((t=i(t))<0)throw RangeError("length is not a small enough positive integer.");Object.defineProperty(this,"length",{value:t}),Object.defineProperty(this,"byteLength",{value:t*this.BYTES_PER_ELEMENT}),Object.defineProperty(this,"buffer",{value:new a(this.byteLength)}),Object.defineProperty(this,"byteOffset",{value:0})}.apply(this,arguments);if(arguments.length>=1&&"object"===e(arguments[0])&&arguments[0]instanceof h)return function(t){if(this.constructor!==t.constructor)throw TypeError();var e=t.length*this.BYTES_PER_ELEMENT;Object.defineProperty(this,"buffer",{value:new a(e)}),Object.defineProperty(this,"byteLength",{value:e}),Object.defineProperty(this,"byteOffset",{value:0}),Object.defineProperty(this,"length",{value:t.length});for(var r=0;r<this.length;r+=1)this._setter(r,t._getter(r))}.apply(this,arguments);if(arguments.length>=1&&"object"===e(arguments[0])&&!(arguments[0]instanceof h)&&!(arguments[0]instanceof a||"ArrayBuffer"===r(arguments[0])))return function(t){var e=t.length*this.BYTES_PER_ELEMENT;Object.defineProperty(this,"buffer",{value:new a(e)}),Object.defineProperty(this,"byteLength",{value:e}),Object.defineProperty(this,"byteOffset",{value:0}),Object.defineProperty(this,"length",{value:t.length});for(var r=0;r<this.length;r+=1){var n=t[r];this._setter(r,Number(n))}}.apply(this,arguments);if(arguments.length>=1&&"object"===e(arguments[0])&&(arguments[0]instanceof a||"ArrayBuffer"===r(arguments[0])))return function(t,e,r){if((e=f(e))>t.byteLength)throw RangeError("byteOffset out of range");if(e%this.BYTES_PER_ELEMENT)throw RangeError("buffer length minus the byteOffset is not a multiple of the element size.");if(r===B){var n=t.byteLength-e;if(n%this.BYTES_PER_ELEMENT)throw RangeError("length of buffer minus byteOffset not a multiple of the element size");r=n/this.BYTES_PER_ELEMENT}else r=f(r),n=r*this.BYTES_PER_ELEMENT;if(e+n>t.byteLength)throw RangeError("byteOffset and length reference an area beyond the end of the buffer");Object.defineProperty(this,"buffer",{value:t}),Object.defineProperty(this,"byteLength",{value:n}),Object.defineProperty(this,"byteOffset",{value:e}),Object.defineProperty(this,"length",{value:r})}.apply(this,arguments);throw TypeError()}function P(t,e,r){var n=function(){Object.defineProperty(this,"constructor",{value:n}),h.apply(this,arguments),u(this)};"__proto__"in n?n.__proto__=h:(n.from=h.from,n.of=h.of),n.BYTES_PER_ELEMENT=t;var o=function(){};return o.prototype=T,n.prototype=new o,Object.defineProperty(n.prototype,"BYTES_PER_ELEMENT",{value:t}),Object.defineProperty(n.prototype,"_pack",{value:e}),Object.defineProperty(n.prototype,"_unpack",{value:r}),n}t.ArrayBuffer=t.ArrayBuffer||a,Object.defineProperty(h,"from",{value:function(t){return new this(t)}}),Object.defineProperty(h,"of",{value:function(){return new this(arguments)}});var T={};h.prototype=T,Object.defineProperty(h.prototype,"_getter",{value:function(t){if(arguments.length<1)throw SyntaxError("Not enough arguments");if((t=f(t))>=this.length)return B;var e,r,n=[];for(e=0,r=this.byteOffset+t*this.BYTES_PER_ELEMENT;e<this.BYTES_PER_ELEMENT;e+=1,r+=1)n.push(this.buffer._bytes[r]);return this._unpack(n)}}),Object.defineProperty(h.prototype,"get",{value:h.prototype._getter}),Object.defineProperty(h.prototype,"_setter",{value:function(t,e){if(arguments.length<2)throw SyntaxError("Not enough arguments");if(!((t=f(t))>=this.length)){var r,n,o=this._pack(e);for(r=0,n=this.byteOffset+t*this.BYTES_PER_ELEMENT;r<this.BYTES_PER_ELEMENT;r+=1,n+=1)this.buffer._bytes[n]=o[r]}}}),Object.defineProperty(h.prototype,"constructor",{value:h}),Object.defineProperty(h.prototype,"copyWithin",{value:function(t,e){var r=arguments[2],n=o(this),u=n.length,a=f(u);a=U(a,0);var h,y=i(t);h=y<0?U(a+y,0):Y(y,a);var s,l=i(e);s=l<0?U(a+l,0):Y(l,a);var p;p=r===B?a:i(r);var c;c=p<0?U(a+p,0):Y(p,a);var b,g=Y(c-s,a-h);for(s<h&&h<s+g?(b=-1,s=s+g-1,h=h+g-1):b=1;g>0;)n._setter(h,n._getter(s)),s+=b,h+=b,g-=1;return n}}),Object.defineProperty(h.prototype,"every",{value:function(t){if(this===B||null===this)throw TypeError();var e=Object(this),r=f(e.length);if(!n(t))throw TypeError();for(var o=arguments[1],i=0;i<r;i++)if(!t.call(o,e._getter(i),i,e))return!1;return!0}}),Object.defineProperty(h.prototype,"fill",{value:function(t){var e=arguments[1],r=arguments[2],n=o(this),u=n.length,a=f(u);a=U(a,0);var h,y=i(e);h=y<0?U(a+y,0):Y(y,a);var s;s=r===B?a:i(r);var l;for(l=s<0?U(a+s,0):Y(s,a);h<l;)n._setter(h,t),h+=1;return n}}),Object.defineProperty(h.prototype,"filter",{value:function(t){if(this===B||null===this)throw TypeError();var e=Object(this),r=f(e.length);if(!n(t))throw TypeError();for(var o=[],i=arguments[1],u=0;u<r;u++){var a=e._getter(u);t.call(i,a,u,e)&&o.push(a)}return new this.constructor(o)}}),Object.defineProperty(h.prototype,"find",{value:function(t){var e=o(this),r=e.length,i=f(r);if(!n(t))throw TypeError();for(var u=arguments.length>1?arguments[1]:B,a=0;a<i;){var h=e._getter(a),y=t.call(u,h,a,e);if(Boolean(y))return h;++a}return B}}),Object.defineProperty(h.prototype,"findIndex",{value:function(t){var e=o(this),r=e.length,i=f(r);if(!n(t))throw TypeError();for(var u=arguments.length>1?arguments[1]:B,a=0;a<i;){var h=e._getter(a),y=t.call(u,h,a,e);if(Boolean(y))return a;++a}return-1}}),Object.defineProperty(h.prototype,"forEach",{value:function(t){if(this===B||null===this)throw TypeError();var e=Object(this),r=f(e.length);if(!n(t))throw TypeError();for(var o=arguments[1],i=0;i<r;i++)t.call(o,e._getter(i),i,e)}}),Object.defineProperty(h.prototype,"indexOf",{value:function(t){if(this===B||null===this)throw TypeError();var e=Object(this),r=f(e.length);if(0===r)return-1;var n=0;if(arguments.length>0&&(n=Number(arguments[1]),n!==n?n=0:0!==n&&n!==1/0&&n!==-1/0&&(n=(n>0||-1)*m(M(n)))),n>=r)return-1;for(var o=n>=0?n:U(r-M(n),0);o<r;o++)if(e._getter(o)===t)return o;return-1}}),Object.defineProperty(h.prototype,"join",{value:function(t){if(this===B||null===this)throw TypeError();for(var e=Object(this),r=f(e.length),n=Array(r),o=0;o<r;++o)n[o]=e._getter(o);return n.join(t===B?",":t)}}),Object.defineProperty(h.prototype,"lastIndexOf",{value:function(t){if(this===B||null===this)throw TypeError();var e=Object(this),r=f(e.length);if(0===r)return-1;var n=r;arguments.length>1&&(n=Number(arguments[1]),n!==n?n=0:0!==n&&n!==1/0&&n!==-1/0&&(n=(n>0||-1)*m(M(n))));for(var o=n>=0?Y(n,r-1):r-M(n);o>=0;o--)if(e._getter(o)===t)return o;return-1}}),Object.defineProperty(h.prototype,"map",{value:function(t){if(this===B||null===this)throw TypeError();var e=Object(this),r=f(e.length);if(!n(t))throw TypeError();var o=[];o.length=r;for(var i=arguments[1],u=0;u<r;u++)o[u]=t.call(i,e._getter(u),u,e);return new this.constructor(o)}}),Object.defineProperty(h.prototype,"reduce",{value:function(t){if(this===B||null===this)throw TypeError();var e=Object(this),r=f(e.length);if(!n(t))throw TypeError();if(0===r&&1===arguments.length)throw TypeError();var o,i=0;for(o=arguments.length>=2?arguments[1]:e._getter(i++);i<r;)o=t.call(B,o,e._getter(i),i,e),i++;return o}}),Object.defineProperty(h.prototype,"reduceRight",{value:function(t){if(this===B||null===this)throw TypeError();var e=Object(this),r=f(e.length);if(!n(t))throw TypeError();if(0===r&&1===arguments.length)throw TypeError();var o,i=r-1;for(o=arguments.length>=2?arguments[1]:e._getter(i--);i>=0;)o=t.call(B,o,e._getter(i),i,e),i--;return o}}),Object.defineProperty(h.prototype,"reverse",{value:function(){if(this===B||null===this)throw TypeError();for(var t=Object(this),e=f(t.length),r=m(e/2),n=0,o=e-1;n<r;++n,--o){var i=t._getter(n);t._setter(n,t._getter(o)),t._setter(o,i)}return t}}),Object.defineProperty(h.prototype,"set",{value:function(t,e){if(arguments.length<1)throw SyntaxError("Not enough arguments");var r,n,o,i,u,a,h,y,s,l;if("object"==typeof arguments[0]&&arguments[0].constructor===this.constructor){if(r=arguments[0],(o=f(arguments[1]))+r.length>this.length)throw RangeError("Offset plus length of array is out of range");if(y=this.byteOffset+o*this.BYTES_PER_ELEMENT,s=r.length*this.BYTES_PER_ELEMENT,r.buffer===this.buffer){for(l=[],u=0,a=r.byteOffset;u<s;u+=1,a+=1)l[u]=r.buffer._bytes[a];for(u=0,h=y;u<s;u+=1,h+=1)this.buffer._bytes[h]=l[u]}else for(u=0,a=r.byteOffset,h=y;u<s;u+=1,a+=1,h+=1)this.buffer._bytes[h]=r.buffer._bytes[a]}else{if("object"!=typeof arguments[0]||"undefined"==typeof arguments[0].length)throw TypeError("Unexpected argument type(s)");if(n=arguments[0],i=f(n.length),(o=f(arguments[1]))+i>this.length)throw RangeError("Offset plus length of array is out of range");for(u=0;u<i;u+=1)a=n[u],this._setter(o+u,Number(a))}}}),Object.defineProperty(h.prototype,"slice",{value:function(t,e){for(var r=o(this),n=r.length,u=f(n),a=i(t),h=a<0?U(u+a,0):Y(a,u),y=e===B?u:i(e),s=y<0?U(u+y,0):Y(y,u),l=s-h,p=r.constructor,c=new p(l),b=0;h<s;){var g=r._getter(h);c._setter(b,g),++h,++b}return c}}),Object.defineProperty(h.prototype,"some",{value:function(t){if(this===B||null===this)throw TypeError();var e=Object(this),r=f(e.length);if(!n(t))throw TypeError();for(var o=arguments[1],i=0;i<r;i++)if(t.call(o,e._getter(i),i,e))return!0;return!1}}),Object.defineProperty(h.prototype,"sort",{value:function(t){function e(e,r){return e!==e&&r!==r?0:e!==e?1:r!==r?-1:t!==B?t(e,r):e<r?-1:e>r?1:0}if(this===B||null===this)throw TypeError();for(var r=Object(this),n=f(r.length),o=Array(n),i=0;i<n;++i)o[i]=r._getter(i);for(o.sort(e),i=0;i<n;++i)r._setter(i,o[i]);return r}}),Object.defineProperty(h.prototype,"subarray",{value:function(t,e){function r(t,e,r){return t<e?e:t>r?r:t}t=i(t),e=i(e),arguments.length<1&&(t=0),arguments.length<2&&(e=this.length),t<0&&(t=this.length+t),e<0&&(e=this.length+e),t=r(t,0,this.length),e=r(e,0,this.length);var n=e-t;return n<0&&(n=0),new this.constructor(this.buffer,this.byteOffset+t*this.BYTES_PER_ELEMENT,n)}});var N=P(1,y,s),S=P(1,l,p),I=P(1,c,p),F=P(2,b,g),x=P(2,E,v),k=P(4,_,O),C=P(4,d,j),z=P(4,R,L),D=P(8,A,w);t.Int8Array=t.Int8Array||N,t.Uint8Array=t.Uint8Array||S,t.Uint8ClampedArray=t.Uint8ClampedArray||I,t.Int16Array=t.Int16Array||F,t.Uint16Array=t.Uint16Array||x,t.Int32Array=t.Int32Array||k,t.Uint32Array=t.Uint32Array||C,t.Float32Array=t.Float32Array||z,t.Float64Array=t.Float64Array||D}(),function(){function e(t,e){return n(t.get)?t.get(e):t[e]}function o(t,e,n){if(!(t instanceof ArrayBuffer||"ArrayBuffer"===r(t)))throw TypeError();if((e=f(e))>t.byteLength)throw RangeError("byteOffset out of range");if(n=n===B?t.byteLength-e:f(n),e+n>t.byteLength)throw RangeError("byteOffset and length reference an area beyond the end of the buffer");Object.defineProperty(this,"buffer",{value:t}),Object.defineProperty(this,"byteLength",{value:n}),Object.defineProperty(this,"byteOffset",{value:e})}function i(t){return function r(n,o){if((n=f(n))+t.BYTES_PER_ELEMENT>this.byteLength)throw RangeError("Array index out of range");n+=this.byteOffset;for(var i=new Uint8Array(this.buffer,n,t.BYTES_PER_ELEMENT),u=[],h=0;h<t.BYTES_PER_ELEMENT;h+=1)u.push(e(i,h));return Boolean(o)===Boolean(a)&&u.reverse(),e(new t(new Uint8Array(u).buffer),0)}}function u(t){return function r(n,o,i){if((n=f(n))+t.BYTES_PER_ELEMENT>this.byteLength)throw RangeError("Array index out of range");var u,h,y=new t([o]),s=new Uint8Array(y.buffer),l=[];for(u=0;u<t.BYTES_PER_ELEMENT;u+=1)l.push(e(s,u));Boolean(i)===Boolean(a)&&l.reverse(),h=new Uint8Array(this.buffer,n,t.BYTES_PER_ELEMENT),h.set(l)}}var a=function(){var t=new Uint16Array([4660]);return 18===e(new Uint8Array(t.buffer),0)}();Object.defineProperty(o.prototype,"getUint8",{value:i(Uint8Array)}),Object.defineProperty(o.prototype,"getInt8",{value:i(Int8Array)}),Object.defineProperty(o.prototype,"getUint16",{value:i(Uint16Array)}),Object.defineProperty(o.prototype,"getInt16",{value:i(Int16Array)}),Object.defineProperty(o.prototype,"getUint32",{value:i(Uint32Array)}),Object.defineProperty(o.prototype,"getInt32",{value:i(Int32Array)}),Object.defineProperty(o.prototype,"getFloat32",{value:i(Float32Array)}),Object.defineProperty(o.prototype,"getFloat64",{value:i(Float64Array)}),Object.defineProperty(o.prototype,"setUint8",{value:u(Uint8Array)}),Object.defineProperty(o.prototype,"setInt8",{value:u(Int8Array)}),Object.defineProperty(o.prototype,"setUint16",{value:u(Uint16Array)}),Object.defineProperty(o.prototype,"setInt16",{value:u(Int16Array)}),Object.defineProperty(o.prototype,"setUint32",{value:u(Uint32Array)}),Object.defineProperty(o.prototype,"setInt32",{value:u(Int32Array)}),Object.defineProperty(o.prototype,"setFloat32",{value:u(Float32Array)}),Object.defineProperty(o.prototype,"setFloat64",{value:u(Float64Array)}),t.DataView=t.DataView||o}()}(self);if (!("Date"in self&&"now"in self.Date&&"getTime"in self.Date.prototype
)) {Date.now=function e(){return(new Date).getTime()};}if (!("Date"in self&&"toISOString"in Date.prototype
)) {Date.prototype.toISOString=function t(){function t(t,e){return t=""+t,"0000".substr(0,e-t.length)+t}var e=this;return e.getUTCFullYear()+"-"+t(e.getUTCMonth()+1,2)+"-"+t(e.getUTCDate(),2)+"T"+t(e.getUTCHours(),2)+":"+t(e.getUTCMinutes(),2)+":"+t(e.getUTCSeconds(),2)+"."+t(e.getUTCMilliseconds(),3)+"Z"};}if (!("document"in self&&"getAttributeNames"in document.documentElement
)) {!function(t){t.Element.prototype.getAttributeNames=function e(){for(var t=this.attributes,e=t.length,n=new Array(e),r=0;r<e;r++)n[r]=t[r].name;return n}}(self);}if (!("JSON"in self
)) {(function(){function e(t,r){function i(e,t){try{e()}catch(n){t&&t()}}function c(e){if(null!=c[e])return c[e];var t;if("bug-string-char-index"==e)t="a"!="a"[0];else if("json"==e)t=c("json-stringify")&&c("date-serialization")&&c("json-parse");else if("date-serialization"==e){if(t=c("json-stringify")&&v){var n=r.stringify;i(function(){t='"-271821-04-20T00:00:00.000Z"'==n(new s(-864e13))&&'"+275760-09-13T00:00:00.000Z"'==n(new s(864e13))&&'"-000001-01-01T00:00:00.000Z"'==n(new s(-621987552e5))&&'"1969-12-31T23:59:59.999Z"'==n(new s(-1))})}}else{var o,a='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}';if("json-stringify"==e){var n=r.stringify,u="function"==typeof n;u&&((o=function(){return 1}).toJSON=o,i(function(){u="0"===n(0)&&"0"===n(new f)&&'""'==n(new l)&&n(j)===b&&n(b)===b&&n()===b&&"1"===n(o)&&"[1]"==n([o])&&"[null]"==n([b])&&"null"==n(null)&&"[null,null,null]"==n([b,j,null])&&n({a:[o,!0,!1,null,"\0\b\n\f\r\t"]})==a&&"1"===n(null,o)&&"[\n 1,\n 2\n]"==n([1,2],null,1)},function(){u=!1})),t=u}if("json-parse"==e){var p,g=r.parse;"function"==typeof g&&i(function(){0!==g("0")||g(!1)||(o=g(a),(p=5==o.a.length&&1===o.a[0])&&(i(function(){p=!g('"\t"')}),p&&i(function(){p=1!==g("01")}),p&&i(function(){p=1!==g("1.")})))},function(){p=!1}),t=p}}return c[e]=!!t}function a(e){return A(this)}t||(t=o.Object()),r||(r=o.Object());var f=t.Number||o.Number,l=t.String||o.String,u=t.Object||o.Object,s=t.Date||o.Date,p=t.SyntaxError||o.SyntaxError,g=t.TypeError||o.TypeError,h=t.Math||o.Math,y=t.JSON||o.JSON;"object"==typeof y&&y&&(r.stringify=y.stringify,r.parse=y.parse);var b,d=u.prototype,j=d.toString,C=d.hasOwnProperty,v=new s(-0xc782b5b800cec);if(i(function(){v=-109252==v.getUTCFullYear()&&0===v.getUTCMonth()&&1===v.getUTCDate()&&10==v.getUTCHours()&&37==v.getUTCMinutes()&&6==v.getUTCSeconds()&&708==v.getUTCMilliseconds()}),c["bug-string-char-index"]=c["date-serialization"]=c.json=c["json-stringify"]=c["json-parse"]=null,!c("json")){var S=c("bug-string-char-index"),O=function(e,t){var r,o,i,c=0;(r=function(){this.valueOf=0}).prototype.valueOf=0,o=new r;for(i in o)C.call(o,i)&&c++;return r=o=null,c?O=function(e,t){var n,r,o="[object Function]"==j.call(e);for(n in e)o&&"prototype"==n||!C.call(e,n)||(r="constructor"===n)||t(n);(r||C.call(e,n="constructor"))&&t(n)}:(o=["valueOf","toString","toLocaleString","propertyIsEnumerable","isPrototypeOf","hasOwnProperty","constructor"],O=function(e,t){var r,i,c="[object Function]"==j.call(e),a=!c&&"function"!=typeof e.constructor&&n[typeof e.hasOwnProperty]&&e.hasOwnProperty||C;for(r in e)c&&"prototype"==r||!a.call(e,r)||t(r);for(i=o.length;r=o[--i];)a.call(e,r)&&t(r)}),O(e,t)};if(!c("json-stringify")&&!c("date-serialization")){var T={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},w=function(e,t){return("000000"+(t||0)).slice(-e)},A=function(e){var t,n,r,o,i,c,a,f,l;if(v)t=function(e){n=e.getUTCFullYear(),r=e.getUTCMonth(),o=e.getUTCDate(),c=e.getUTCHours(),a=e.getUTCMinutes(),f=e.getUTCSeconds(),l=e.getUTCMilliseconds()};else{var u=h.floor,s=[0,31,59,90,120,151,181,212,243,273,304,334],p=function(e,t){return s[t]+365*(e-1970)+u((e-1969+(t=+(t>1)))/4)-u((e-1901+t)/100)+u((e-1601+t)/400)};t=function(e){for(o=u(e/864e5),n=u(o/365.2425)+1970-1;p(n+1,0)<=o;n++);for(r=u((o-p(n,0))/30.42);p(n,r+1)<=o;r++);o=1+o-p(n,r),i=(e%864e5+864e5)%864e5,c=u(i/36e5)%24,a=u(i/6e4)%60,f=u(i/1e3)%60,l=i%1e3}}return(A=function(e){return e>-1/0&&e<1/0?(t(e),e=(n<=0||n>=1e4?(n<0?"-":"+")+w(6,n<0?-n:n):w(4,n))+"-"+w(2,r+1)+"-"+w(2,o)+"T"+w(2,c)+":"+w(2,a)+":"+w(2,f)+"."+w(3,l)+"Z",n=r=o=c=a=f=l=null):e=null,e})(e)};if(c("json-stringify")&&!c("date-serialization")){var N=r.stringify;r.stringify=function(e,t,n){var r=s.prototype.toJSON;s.prototype.toJSON=a;var o=N(e,t,n);return s.prototype.toJSON=r,o}}else{var x=function(e){var t=e.charCodeAt(0),n=T[t];return n||"\\u00"+w(2,t.toString(16))},J=/[\x00-\x1f\x22\x5c]/g,U=function(e){return J.lastIndex=0,'"'+(J.test(e)?e.replace(J,x):e)+'"'},m=function(e,t,n,r,o,c,a){var f,l,u,p,h,y,d,C,v;if(i(function(){f=t[e]}),"object"==typeof f&&f&&(f.getUTCFullYear&&"[object Date]"==j.call(f)&&f.toJSON===s.prototype.toJSON?f=A(f):"function"==typeof f.toJSON&&(f=f.toJSON(e))),n&&(f=n.call(t,e,f)),f==b)return f===b?f:"null";switch(l=typeof f,"object"==l&&(u=j.call(f)),u||l){case"boolean":case"[object Boolean]":return""+f;case"number":case"[object Number]":return f>-1/0&&f<1/0?""+f:"null";case"string":case"[object String]":return U(""+f)}if("object"==typeof f){for(d=a.length;d--;)if(a[d]===f)throw g();if(a.push(f),p=[],C=c,c+=o,"[object Array]"==u){for(y=0,d=f.length;y<d;y++)h=m(y,f,n,r,o,c,a),p.push(h===b?"null":h);v=p.length?o?"[\n"+c+p.join(",\n"+c)+"\n"+C+"]":"["+p.join(",")+"]":"[]"}else O(r||f,function(e){var t=m(e,f,n,r,o,c,a);t!==b&&p.push(U(e)+":"+(o?" ":"")+t)}),v=p.length?o?"{\n"+c+p.join(",\n"+c)+"\n"+C+"}":"{"+p.join(",")+"}":"{}";return a.pop(),v}};r.stringify=function(e,t,r){var o,i,c,a;if(n[typeof t]&&t)if("[object Function]"==(a=j.call(t)))i=t;else if("[object Array]"==a){c={};for(var f,l=0,u=t.length;l<u;)f=t[l++],"[object String]"!=(a=j.call(f))&&"[object Number]"!=a||(c[f]=1)}if(r)if("[object Number]"==(a=j.call(r))){if((r-=r%1)>0)for(r>10&&(r=10),o="";o.length<r;)o+=" "}else"[object String]"==a&&(o=r.length<=10?r:r.slice(0,10));return m("",(f={},f[""]=e,f),i,c,o,"",[])}}}if(!c("json-parse")){var M,F,z=l.fromCharCode,D={92:"\\",34:'"',47:"/",98:"\b",116:"\t",110:"\n",102:"\f",114:"\r"},E=function(){throw M=F=null,p()},P=function(){for(var e,t,n,r,o,i=F,c=i.length;M<c;)switch(o=i.charCodeAt(M)){case 9:case 10:case 13:case 32:M++;break;case 123:case 125:case 91:case 93:case 58:case 44:return e=S?i.charAt(M):i[M],M++,e;case 34:for(e="@",M++;M<c;)if((o=i.charCodeAt(M))<32)E();else if(92==o)switch(o=i.charCodeAt(++M)){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:e+=D[o],M++;break;case 117:for(t=++M,n=M+4;M<n;M++)(o=i.charCodeAt(M))>=48&&o<=57||o>=97&&o<=102||o>=65&&o<=70||E();e+=z("0x"+i.slice(t,M));break;default:E()}else{if(34==o)break;for(o=i.charCodeAt(M),t=M;o>=32&&92!=o&&34!=o;)o=i.charCodeAt(++M);e+=i.slice(t,M)}if(34==i.charCodeAt(M))return M++,e;E();default:if(t=M,45==o&&(r=!0,o=i.charCodeAt(++M)),o>=48&&o<=57){for(48==o&&(o=i.charCodeAt(M+1))>=48&&o<=57&&E(),r=!1;M<c&&(o=i.charCodeAt(M))>=48&&o<=57;M++);if(46==i.charCodeAt(M)){for(n=++M;n<c&&!((o=i.charCodeAt(n))<48||o>57);n++);n==M&&E(),M=n}if(101==(o=i.charCodeAt(M))||69==o){for(o=i.charCodeAt(++M),43!=o&&45!=o||M++,n=M;n<c&&!((o=i.charCodeAt(n))<48||o>57);n++);n==M&&E(),M=n}return+i.slice(t,M)}r&&E();var a=i.slice(M,M+4);if("true"==a)return M+=4,!0;if("fals"==a&&101==i.charCodeAt(M+4))return M+=5,!1;if("null"==a)return M+=4,null;E()}return"$"},Z=function(e){var t,n;if("$"==e&&E(),"string"==typeof e){if("@"==(S?e.charAt(0):e[0]))return e.slice(1);if("["==e){for(t=[];"]"!=(e=P());)n?","==e?"]"==(e=P())&&E():E():n=!0,","==e&&E(),t.push(Z(e));return t}if("{"==e){for(t={};"}"!=(e=P());)n?","==e?"}"==(e=P())&&E():E():n=!0,","!=e&&"string"==typeof e&&"@"==(S?e.charAt(0):e[0])&&":"==P()||E(),t[e.slice(1)]=Z(P());return t}E()}return e},k=function(e,t,n){var r=I(e,t,n);r===b?delete e[t]:e[t]=r},I=function(e,t,n){var r,o=e[t];if("object"==typeof o&&o)if("[object Array]"==j.call(o))for(r=o.length;r--;)k(j,O,o);else O(o,function(e){k(o,e,n)});return n.call(e,t,o)};r.parse=function(e,t){var n,r;return M=0,F=""+e,n=Z(P()),"$"!=P()&&E(),M=F=null,t&&"[object Function]"==j.call(t)?I((r={},r[""]=n,r),"",t):n}}}return r.runInContext=e,r}var t="function"==typeof define&&define.amd,n={"function":!0,object:!0},r=n[typeof exports]&&exports&&!exports.nodeType&&exports,o=n[typeof window]&&window||this,i=r&&n[typeof module]&&module&&!module.nodeType&&"object"==typeof global&&global;if(!i||i.global!==i&&i.window!==i&&i.self!==i||(o=i),r&&!t)e(o,r);else{var c=o.JSON,a=o.JSON3,f=!1,l=e(o,o.JSON3={noConflict:function(){return f||(f=!0,o.JSON=c,o.JSON3=a,c=a=null),l}});o.JSON={parse:l.parse,stringify:l.stringify}}t&&define(function(){return l})}).call(this);}if (!("MutationObserver"in self
)) {window.MutationObserver||(window.MutationObserver=function(t){"use strict";function e(t){this._watched=[],this._listener=t}function a(t){!function a(){var r=t.takeRecords();r.length&&t._listener(r,t),t._timeout=setTimeout(a,e._period)}()}function r(e){var a={type:null,target:null,addedNodes:[],removedNodes:[],previousSibling:null,nextSibling:null,attributeName:null,attributeNamespace:null,oldValue:null};for(var r in e)g(a,r)&&e[r]!==t&&(a[r]=e[r]);return a}function n(t,e){var a=s(t,e);return function(n){var i,u=n.length;e.charData&&3===t.nodeType&&t.nodeValue!==a.charData&&n.push(new r({type:"characterData",target:t,oldValue:a.charData})),e.attr&&a.attr&&o(n,t,a.attr,e.afilter),(e.kids||e.descendents)&&(i=l(n,t,a,e)),(i||n.length!==u)&&(a=s(t,e))}}function i(t,e){return e.value}function u(t,e){return"style"!==e.name?e.value:t.style.cssText}function o(t,e,a,n){for(var i,u,o={},l=e.attributes,s=l.length;s--;)i=l[s],u=i.name,n&&!g(n,u)||(m(e,i)!==a[u]&&t.push(r({type:"attributes",target:e,attributeName:u,oldValue:a[u],attributeNamespace:i.namespaceURI})),o[u]=!0);for(u in a)o[u]||t.push(r({target:e,type:"attributes",attributeName:u,oldValue:a[u]}))}function l(e,a,n,i){function u(t,a,n,u,s){for(var d,c,h,f=t.length-1,p=-~((f-s)/2);h=t.pop();)d=n[h.i],c=u[h.j],i.kids&&p&&Math.abs(h.i-h.j)>=f&&(e.push(r({type:"childList",target:a,addedNodes:[d],removedNodes:[d],nextSibling:d.nextSibling,previousSibling:d.previousSibling})),p--),i.attr&&c.attr&&o(e,d,c.attr,i.afilter),i.charData&&3===d.nodeType&&d.nodeValue!==c.charData&&e.push(r({type:"characterData",target:d,oldValue:c.charData})),i.descendents&&l(d,c)}function l(a,n){for(var h,f,g,v,b,m,y,D=a.childNodes,N=n.kids,V=D.length,_=N?N.length:0,k=0,S=0,w=0;S<V||w<_;)m=D[S],b=N[w],y=b&&b.node,m===y?(i.attr&&b.attr&&o(e,m,b.attr,i.afilter),i.charData&&b.charData!==t&&m.nodeValue!==b.charData&&e.push(r({type:"characterData",target:m,oldValue:b.charData})),f&&u(f,a,D,N,k),i.descendents&&(m.childNodes.length||b.kids&&b.kids.length)&&l(m,b),S++,w++):(s=!0,h||(h={},f=[]),m&&(h[g=c(m)]||(h[g]=!0,-1===(v=d(N,m,w))?i.kids&&(e.push(r({type:"childList",target:a,addedNodes:[m],nextSibling:m.nextSibling,previousSibling:m.previousSibling})),k++):f.push({i:S,j:v})),S++),y&&y!==D[S]&&(h[g=c(y)]||(h[g]=!0,-1===(v=p(D,y,S))?i.kids&&(e.push(r({type:"childList",target:n.node,removedNodes:[y],nextSibling:N[w+1],previousSibling:N[w-1]})),k--):f.push({i:v,j:w})),w++));f&&u(f,a,D,N,k)}var s;return l(a,n),s}function s(t,e){var a=!0;return function r(t){var n={node:t};return!e.charData||3!==t.nodeType&&8!==t.nodeType?(e.attr&&a&&1===t.nodeType&&(n.attr=f(t.attributes,function(a,r){return e.afilter&&!e.afilter[r.name]||(a[r.name]=m(t,r)),a},{})),a&&(e.kids||e.charData||e.attr&&e.descendents)&&(n.kids=h(t.childNodes,r)),a=e.descendents):n.charData=t.nodeValue,n}(t)}function d(t,e,a){return p(t,e,a,v("node"))}function c(t){try{return t.id||(t[D]=t[D]||y++)}catch(e){try{return t.nodeValue}catch(a){return y++}}}function h(t,e){for(var a=[],r=0;r<t.length;r++)a[r]=e(t[r],r,t);return a}function f(t,e,a){for(var r=0;r<t.length;r++)a=e(a,t[r],r,t);return a}function p(t,e,a,r){for(;a<t.length;a++)if((r?t[a][r]:t[a])===e)return a;return-1}function g(e,a){return e[a]!==t}function v(t){return t}e._period=30,e.prototype={observe:function(t,e){for(var r={attr:!!(e.attributes||e.attributeFilter||e.attributeOldValue),kids:!!e.childList,descendents:!!e.subtree,charData:!(!e.characterData&&!e.characterDataOldValue)},i=this._watched,u=0;u<i.length;u++)i[u].tar===t&&i.splice(u,1);e.attributeFilter&&(r.afilter=f(e.attributeFilter,function(t,e){return t[e]=!0,t},{})),i.push({tar:t,fn:n(t,r)}),this._timeout||a(this)},takeRecords:function(){for(var t=[],e=this._watched,a=0;a<e.length;a++)e[a].fn(t);return t},disconnect:function(){this._watched=[],clearTimeout(this._timeout),this._timeout=null}};var b=document.createElement("i");b.style.top=0,b="null"!=b.attributes.style.value;var m=b?i:u,y=1,D="mo_id";return e}(void 0));}if (!("defineProperty"in Object&&function(){try{var e={}
return Object.defineProperty(e,"test",{value:42}),!0}catch(t){return!1}}()
)) {!function(e){var t=Object.prototype.hasOwnProperty.call(Object.prototype,"__defineGetter__"),r="A property cannot both have accessors and be writable or have a value";Object.defineProperty=function n(o,i,f){if(e&&(o===window||o===document||o===Element.prototype||o instanceof Element))return e(o,i,f);if(null===o||!(o instanceof Object||"object"==typeof o))throw new TypeError("Object.defineProperty called on non-object");if(!(f instanceof Object))throw new TypeError("Property description must be an object");var c=String(i),a="value"in f||"writable"in f,p="get"in f&&typeof f.get,s="set"in f&&typeof f.set;if(p){if(p===undefined)return o;if("function"!==p)throw new TypeError("Getter must be a function");if(!t)throw new TypeError("Getters & setters cannot be defined on this javascript engine");if(a)throw new TypeError(r);Object.__defineGetter__.call(o,c,f.get)}else o[c]=f.value;if(s){if(s===undefined)return o;if("function"!==s)throw new TypeError("Setter must be a function");if(!t)throw new TypeError("Getters & setters cannot be defined on this javascript engine");if(a)throw new TypeError(r);Object.__defineSetter__.call(o,c,f.set)}return"value"in f&&(o[c]=f.value),o}}(Object.defineProperty);}if (!("name"in Function.prototype
)) {!function(){var n=/^\s*function\s+([^(\s]*)\s*/,t=Function,e=t.prototype,r=e.constructor,o=function(o){var c,u;return o===t||o===r?u="Function":o!==e&&(c=(""+o).match(n),u=c&&c[1]),u||""};Object.defineProperty(e,"name",{get:function c(){var n=this,t=o(n);return n!==e&&Object.defineProperty(n,"name",{value:t,configurable:!0}),t},configurable:!0})}();}if (!("EPSILON"in Number
)) {Object.defineProperty(Number,"EPSILON",{enumerable:!1,configurable:!1,writable:!1,value:Math.pow(2,-52)});}if (!("MAX_SAFE_INTEGER"in Number
)) {Object.defineProperty(Number,"MAX_SAFE_INTEGER",{enumerable:!1,configurable:!1,writable:!1,value:Math.pow(2,53)-1});}if (!("MIN_SAFE_INTEGER"in Number
)) {Object.defineProperty(Number,"MIN_SAFE_INTEGER",{enumerable:!1,configurable:!1,writable:!1,value:-(Math.pow(2,53)-1)});}if (!("Window"in self
)) {"undefined"==typeof WorkerGlobalScope&&"function"!=typeof importScripts&&function(o){o.constructor?o.Window=o.constructor:(o.Window=o.constructor=new Function("return function Window() {}")()).prototype=self}(self);}function ArrayCreate(r){if(1/r==-Infinity&&(r=0),r>Math.pow(2,32)-1)throw new RangeError("Invalid array length");var n=[];return n.length=r,n}function Call(t,l){var n=arguments.length>2?arguments[2]:[];if(!1===IsCallable(t))throw new TypeError(Object.prototype.toString.call(t)+"is not a function.");return t.apply(l,n)}function CreateDataProperty(e,r,t){var a={value:t,writable:!0,enumerable:!0,configurable:!0};try{return Object.defineProperty(e,r,a),!0}catch(n){return!1}}function CreateDataPropertyOrThrow(t,r,o){var e=CreateDataProperty(t,r,o);if(!e)throw new TypeError("Cannot assign value `"+Object.prototype.toString.call(o)+"` to property `"+Object.prototype.toString.call(r)+"` on object `"+Object.prototype.toString.call(t)+"`");return e}function CreateMethodProperty(e,r,t){var a={value:t,writable:!0,enumerable:!1,configurable:!0};Object.defineProperty(e,r,a)}if (!("acosh"in Math
)) {CreateMethodProperty(Math,"acosh",function t(a){return isNaN(a)?NaN:a<1?NaN:1===a?0:a===Infinity?Infinity:Math.log(a+Math.sqrt(a*a-1))});}if (!("asinh"in Math
)) {CreateMethodProperty(Math,"asinh",function n(i){return isNaN(i)?NaN:0===i&&1/i===Infinity?0:0===i&&1/i==-Infinity?-0:i===Infinity?Infinity:i===-Infinity?-Infinity:Math.log(i+Math.sqrt(i*i+1))});}if (!("atanh"in Math
)) {CreateMethodProperty(Math,"atanh",function n(t){return isNaN(t)?NaN:t<-1?NaN:t>1?NaN:-1===t?-Infinity:1===t?Infinity:0===t&&1/t===Infinity?0:0===t&&1/t==-Infinity?-0:Math.log((1+t)/(1-t))/2});}if (!("cbrt"in Math
)) {CreateMethodProperty(Math,"cbrt",function n(t){if(isNaN(t))return NaN;if(0===t&&1/t===Infinity)return 0;if(0===t&&1/t==-Infinity)return-0;if(t===Infinity)return Infinity;if(t===-Infinity)return-Infinity;var i=Math.pow(Math.abs(t),1/3);return t<0?-i:i});}if (!("cosh"in Math
)) {CreateMethodProperty(Math,"cosh",function n(t){if(isNaN(t))return NaN;if(0===t&&1/t===Infinity)return 1;if(0===t&&1/t==-Infinity)return 1;if(t===Infinity)return Infinity;if(t===-Infinity)return Infinity;if((t=Math.abs(t))>709){var i=Math.exp(.5*t);return i/2*i}return((i=Math.exp(t))+1/i)/2});}if (!("expm1"in Math
)) {CreateMethodProperty(Math,"expm1",function n(i){return isNaN(i)?NaN:0===i&&1/i===Infinity?0:0===i&&1/i==-Infinity?-0:i===Infinity?Infinity:i===-Infinity?-1:i>-1e-6&&i<1e-6?i+i*i/2:Math.exp(i)-1});}if (!("fround"in Math
)) {CreateMethodProperty(Math,"fround",function(n){return isNaN(n)?NaN:1/n==+Infinity||1/n==-Infinity||n===+Infinity||n===-Infinity?n:new Float32Array([n])[0]});}if (!("hypot"in Math
)) {CreateMethodProperty(Math,"hypot",function t(n,r){if(0===arguments.length)return 0;for(var i=0,e=0,a=0;a<arguments.length;++a){if(arguments[a]===Infinity)return Infinity;if(arguments[a]===-Infinity)return Infinity;var f=Math.abs(Number(arguments[a]));f>e&&(i*=Math.pow(e/f,2),e=f),0===f&&0===e||(i+=Math.pow(f/e,2))}return e*Math.sqrt(i)});}if (!("log10"in Math
)) {CreateMethodProperty(Math,"log10",function t(e){return Math.log(e)/Math.LN10});}if (!("log1p"in Math
)) {CreateMethodProperty(Math,"log1p",function r(t){if(-1<(t=Number(t))&&t<1){for(var o=t,e=2;e<=300;e++)o+=Math.pow(-1,e-1)*Math.pow(t,e)/e;return o}return Math.log(1+t)});}if (!("log2"in Math
)) {CreateMethodProperty(Math,"log2",function t(e){return Math.log(e)/Math.LN2});}if (!("sign"in Math
)) {CreateMethodProperty(Math,"sign",function i(n){return n=Number(n),isNaN(n)?NaN:1/n==-Infinity?-0:1/n===Infinity?0:n<0?-1:n>0?1:void 0});}if (!("sinh"in Math
)) {CreateMethodProperty(Math,"sinh",function r(t){var a=t<0?-1:1,e=Math.abs(t);if(e<22){if(e<Math.pow(2,-28))return t;var h=Math.exp(e)-1;return e<1?a*(2*h-h*h/(h+1))/2:a*(h+h/(h+1))/2}if(e<709.7822265625)return a*Math.exp(e)/2;var n=Math.exp(.5*e);return(h=a*n/2)*n});}if (!("tanh"in Math
)) {CreateMethodProperty(Math,"tanh",function t(n){var e;return n===Infinity?1:n===-Infinity?-1:((e=Math.exp(2*n))-1)/(e+1)});}if (!("trunc"in Math
)) {CreateMethodProperty(Math,"trunc",function t(r){return r<0?Math.ceil(r):Math.floor(r)});}if (!("freeze"in Object
)) {CreateMethodProperty(Object,"freeze",function e(r){return r});}if (!("getPrototypeOf"in Object
)) {CreateMethodProperty(Object,"getPrototypeOf",function t(o){if(o!==Object(o))throw new TypeError("Object.getPrototypeOf called on non-object");var e=o.__proto__;return e||null===e?e:"function"==typeof o.constructor&&o instanceof o.constructor?o.constructor.prototype:o instanceof Object?Object.prototype:null});}if (!("keys"in Object&&function(){return 2===Object.keys(arguments).length}(1,2)&&function(){try{return Object.keys(""),!0}catch(t){return!1}}()
)) {CreateMethodProperty(Object,"keys",function(){"use strict";function t(){var t;try{t=Object.create({})}catch(r){return!0}return o.call(t,"__proto__")}function r(t){var r=n.call(t),e="[object Arguments]"===r;return e||(e="[object Array]"!==r&&null!==t&&"object"==typeof t&&"number"==typeof t.length&&t.length>=0&&"[object Function]"===n.call(t.callee)),e}var e=Object.prototype.hasOwnProperty,n=Object.prototype.toString,o=Object.prototype.propertyIsEnumerable,c=!o.call({toString:null},"toString"),l=o.call(function(){},"prototype"),i=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],u=function(t){var r=t.constructor;return r&&r.prototype===t},a={$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},f=function(){if("undefined"==typeof window)return!1;for(var t in window)try{if(!a["$"+t]&&e.call(window,t)&&null!==window[t]&&"object"==typeof window[t])try{u(window[t])}catch(r){return!0}}catch(r){return!0}return!1}(),p=function(t){if("undefined"==typeof window||!f)return u(t);try{return u(t)}catch(r){return!1}};return function s(o){var u="[object Function]"===n.call(o),a=r(o),f="[object String]"===n.call(o),s=[];if(o===undefined||null===o)throw new TypeError("Cannot convert undefined or null to object");var y=l&&u;if(f&&o.length>0&&!e.call(o,0))for(var h=0;h<o.length;++h)s.push(String(h));if(a&&o.length>0)for(var g=0;g<o.length;++g)s.push(String(g));else for(var w in o)t()&&"__proto__"===w||y&&"prototype"===w||!e.call(o,w)||s.push(String(w));if(c)for(var d=p(o),$=0;$<i.length;++$)d&&"constructor"===i[$]||!e.call(o,i[$])||s.push(i[$]);return s}}());}function Get(n,t){return n[t]}function HasOwnProperty(r,t){return Object.prototype.hasOwnProperty.call(r,t)}function HasProperty(n,r){return r in n}function IsArray(r){return"[object Array]"===Object.prototype.toString.call(r)}if (!("isArray"in Array
)) {CreateMethodProperty(Array,"isArray",function r(e){return IsArray(e)});}function IsCallable(n){return"function"==typeof n}if (!("bind"in Function.prototype
)) {CreateMethodProperty(Function.prototype,"bind",function t(n){var r=Array,o=Object,e=r.prototype,l=function g(){},p=e.slice,a=e.concat,i=e.push,c=Math.max,u=this;if(!IsCallable(u))throw new TypeError("Function.prototype.bind called on incompatible "+u);for(var y,h=p.call(arguments,1),s=function(){if(this instanceof y){var t=u.apply(this,a.call(h,p.call(arguments)));return o(t)===t?t:this}return u.apply(n,a.call(h,p.call(arguments)))},f=c(0,u.length-h.length),b=[],d=0;d<f;d++)i.call(b,"$"+d);return y=Function("binder","return function ("+b.join(",")+"){ return binder.apply(this, arguments); }")(s),u.prototype&&(l.prototype=u.prototype,y.prototype=new l,l.prototype=null),y});}function RequireObjectCoercible(e){if(null===e||e===undefined)throw TypeError(Object.prototype.toString.call(e)+" is not coercible to Object.");return e}function SameValueNonNumber(e,n){return e===n}function ToBoolean(o){return Boolean(o)}function ToNumber(r){return Number(r)}function ToObject(e){if(null===e||e===undefined)throw TypeError();return Object(e)}function GetV(t,e){return ToObject(t)[e]}function GetMethod(e,n){var r=GetV(e,n);if(null===r||r===undefined)return undefined;if(!1===IsCallable(r))throw new TypeError("Method not callable: "+n);return r}function Invoke(n,e){var t=arguments.length>2?arguments[2]:[],l=GetV(n,e);return Call(l,n,t)}function ToUint32(n){var i=Number(n);return isNaN(i)||1/i===Infinity||1/i==-Infinity||i===Infinity||i===-Infinity?0:(i<0?-1:1)*Math.floor(Math.abs(i))>>>0}if (!("clz32"in Math
)) {CreateMethodProperty(Math,"clz32",function t(r){var e=ToUint32(r);return e?32-e.toString(2).length:32});}if (!("imul"in Math
)) {CreateMethodProperty(Math,"imul",function t(r,e){var n=ToUint32(r),o=ToUint32(e),i=n>>>16&65535,a=65535&n,u=o>>>16&65535,h=65535&o;return a*h+(i*h+a*u<<16>>>0)|0});}function Type(e){switch(typeof e){case"undefined":return"undefined";case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"symbol":return"symbol";default:return null===e?"null":"Symbol"in self&&(e instanceof self.Symbol||e.constructor===self.Symbol)?"symbol":"object"}}if (!("isFinite"in Number
)) {!function(){var e=self;CreateMethodProperty(Number,"isFinite",function i(n){return"number"===Type(n)&&e.isFinite(n)})}();}if (!("isNaN"in Number
)) {!function(){var e=self;CreateMethodProperty(Number,"isNaN",function r(n){return"number"===Type(n)&&!!e.isNaN(n)})}();}if (!("isExtensible"in Object
)) {!function(e){CreateMethodProperty(Object,"isExtensible",function t(n){return"object"===Type(n)&&(!e||e(n))})}(Object.isExtensible);}if (!("seal"in Object&&function(){try{return Object.seal("1"),!0}catch(t){return!1}}()
)) {!function(e){CreateMethodProperty(Object,"seal",function t(c){return"object"===Type(c)?c:e?e(c):c})}(Object.seal);}if (!("flags"in RegExp.prototype
)) {Object.defineProperty(RegExp.prototype,"flags",{configurable:!0,enumerable:!1,get:function(){var e=this;if("object"!==Type(e))throw new TypeError("Method called on incompatible type: must be an object.");var o="";return ToBoolean(Get(e,"global"))&&(o+="g"),ToBoolean(Get(e,"ignoreCase"))&&(o+="i"),ToBoolean(Get(e,"multiline"))&&(o+="m"),ToBoolean(Get(e,"unicode"))&&(o+="u"),ToBoolean(Get(e,"sticky"))&&(o+="y"),o}});}function CreateIterResultObject(e,r){if("boolean"!==Type(r))throw new Error;var t={};return CreateDataProperty(t,"value",e),CreateDataProperty(t,"done",r),t}function GetPrototypeFromConstructor(t,o){var r=Get(t,"prototype");return"object"!==Type(r)&&(r=o),r}function IsConstructor(t){return"object"===Type(t)&&("function"==typeof t&&!!t.prototype)}function IsRegExp(e){if("object"!==Type(e))return!1;var n="Symbol"in self&&"match"in self.Symbol?Get(e,self.Symbol.match):undefined;if(n!==undefined)return ToBoolean(n);try{var t=e.lastIndex;return e.lastIndex=0,RegExp.prototype.exec.call(e),!0}catch(l){}finally{e.lastIndex=t}return!1}function IteratorClose(r,t){if("object"!==Type(r["[[Iterator]]"]))throw new Error(Object.prototype.toString.call(r["[[Iterator]]"])+"is not an Object.");var e=r["[[Iterator]]"],o=GetMethod(e,"return");if(o===undefined)return t;try{var n=Call(o,e)}catch(c){var a=c}if(t)return t;if(a)throw a;if("object"!==Type(n))throw new TypeError("Iterator's return method returned a non-object.");return t}function IteratorComplete(t){if("object"!==Type(t))throw new Error(Object.prototype.toString.call(t)+"is not an Object.");return ToBoolean(Get(t,"done"))}function IteratorNext(t){if(arguments.length<2)var e=Call(t["[[NextMethod]]"],t["[[Iterator]]"]);else e=Call(t["[[NextMethod]]"],t["[[Iterator]]"],[arguments[1]]);if("object"!==Type(e))throw new TypeError("bad iterator");return e}function IteratorStep(t){var r=IteratorNext(t);return!0!==IteratorComplete(r)&&r}function IteratorValue(t){if("object"!==Type(t))throw new Error(Object.prototype.toString.call(t)+"is not an Object.");return Get(t,"value")}function OrdinaryToPrimitive(r,t){if("string"===t)var e=["toString","valueOf"];else e=["valueOf","toString"];for(var i=0;i<e.length;++i){var n=e[i],a=Get(r,n);if(IsCallable(a)){var o=Call(a,r);if("object"!==Type(o))return o}}throw new TypeError("Cannot convert to primitive.")}function RegExpExec(e,l){var r=Get(e,"exec");if(IsCallable(r)){var t=Call(r,e,[l]);if("object"!==Type(t)&&"null"!==Type(t))throw new TypeError("Invalid result: must be an object or null.");return t}return Call(RegExp.prototype.exec,e,[l])}function SameValue(e,a){return Type(e)===Type(a)&&("number"===Type(e)?!(!isNaN(e)||!isNaN(a))||(0!==e||0!==a||1/e==1/a)&&e===a:SameValueNonNumber(e,a))}if (!("is"in Object
)) {CreateMethodProperty(Object,"is",function e(t,r){return SameValue(t,r)});}function SameValueZero(n,e){return Type(n)===Type(e)&&("number"===Type(n)?!(!isNaN(n)||!isNaN(e))||(1/n===Infinity&&1/e==-Infinity||(1/n==-Infinity&&1/e===Infinity||n===e)):SameValueNonNumber(n,e))}function SpeciesConstructor(e,o){var r=Get(e,"constructor");if(r===undefined)return o;if("object"!==Type(r))throw new TypeError("O.constructor is not an Object");var n="function"==typeof self.Symbol&&"symbol"==typeof self.Symbol.species?r[self.Symbol.species]:undefined;if(n===undefined||null===n)return o;if(IsConstructor(n))return n;throw new TypeError("No constructor found")}function ToInteger(n){if("symbol"===Type(n))throw new TypeError("Cannot convert a Symbol value to a number");var t=Number(n);return isNaN(t)?0:1/t===Infinity||1/t==-Infinity||t===Infinity||t===-Infinity?t:(t<0?-1:1)*Math.floor(Math.abs(t))}if (!("copyWithin"in Array.prototype&&function(){try{var t=function n(){}
t.prototype[0]="foo"
var o=new t
o[1]=1,o[2]=2,o.length=3
var r=Array.prototype.copyWithin.call(o,1,0)
return!(!r[0]||Object.prototype.hasOwnProperty.call(r,"0")||!Object.prototype.hasOwnProperty.call(r,"1")||"foo"!==r[0]||"foo"!==r[1]||1!==r[2]||3!==r.length)}catch(e){return!1}}()
)) {CreateMethodProperty(Array.prototype,"copyWithin",function t(r,e){"use strict";var a=arguments[2];if(null===this||this===undefined)throw new TypeError("Cannot call method on "+this);var n=Object(this),i=ToInteger(n.length);i<=0&&(i=0),i=i===Infinity?Math.pow(2,53)-1:Math.min(i,Math.pow(2,53)-1),i=Math.max(i,0);var h,o=ToInteger(r);h=o<0?Math.max(i+o,0):Math.min(o,i);var M,m=ToInteger(e);M=m<0?Math.max(i+m,0):Math.min(m,i);var v;v=a===undefined?i:ToInteger(a);var p;p=v<0?Math.max(i+v,0):Math.min(v,i);var s,d=Math.min(p-M,i-h);for(M<h&&h<M+d?(s=-1,M=M+d-1,h=h+d-1):s=1;d>0;){var f=String(M),g=String(h);if(HasProperty(n,f)){var l=n[f];n[g]=l}else delete n[g];M+=s,h+=s,d-=1}return n});}if (!("isInteger"in Number
)) {CreateMethodProperty(Number,"isInteger",function e(n){return"number"===Type(n)&&(!isNaN(n)&&n!==Infinity&&n!==-Infinity&&ToInteger(n)===n)});}if (!("isSafeInteger"in Number
)) {CreateMethodProperty(Number,"isSafeInteger",function e(r){if("number"!==Type(r))return!1;if(isNaN(r)||r===Infinity||r===-Infinity)return!1;var t=ToInteger(r);return t===r&&Math.abs(t)<=Math.pow(2,53)-1});}function ToLength(n){var t=ToInteger(n);return t<=0?0:Math.min(t,Math.pow(2,53)-1)}function ToPrimitive(e){var t=arguments.length>1?arguments[1]:undefined;if("object"===Type(e)){if(arguments.length<2)var i="default";else t===String?i="string":t===Number&&(i="number");var r="function"==typeof self.Symbol&&"symbol"==typeof self.Symbol.toPrimitive?GetMethod(e,self.Symbol.toPrimitive):undefined;if(r!==undefined){var n=Call(r,e,[i]);if("object"!==Type(n))return n;throw new TypeError("Cannot convert exotic object to primitive.")}return"default"===i&&(i="number"),OrdinaryToPrimitive(e,i)}return e}function ToString(t){switch(Type(t)){case"symbol":throw new TypeError("Cannot convert a Symbol value to a string");case"object":return ToString(ToPrimitive(t,String));default:return String(t)}}if (!("every"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"every",function r(e){var t=ToObject(this),o=ToLength(Get(t,"length"));if(!1===IsCallable(e))throw new TypeError(e+" is not a function");for(var n=arguments.length>1?arguments[1]:undefined,a=0;a<o;){var i=ToString(a);if(HasProperty(t,i)){var l=Get(t,i);if(!1===ToBoolean(Call(e,n,[l,a,t])))return!1}a+=1}return!0});}if (!("fill"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"fill",function t(e){for(var r=arguments[1],n=arguments[2],o=ToObject(this),a=ToLength(Get(o,"length")),h=ToInteger(r),i=h<0?Math.max(a+h,0):Math.min(h,a),g=n===undefined?a:ToInteger(n),M=g<0?Math.max(a+g,0):Math.min(g,a);i<M;){o[ToString(i)]=e,i+=1}return o});}if (!("find"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"find",function e(r){var t=ToObject(this),n=ToLength(Get(t,"length"));if(!1===IsCallable(r))throw new TypeError(r+" is not a function");for(var o=arguments.length>1?arguments[1]:undefined,a=0;a<n;){var i=ToString(a),f=Get(t,i);if(ToBoolean(Call(r,o,[f,a,t])))return f;a+=1}return undefined});}if (!("findIndex"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"findIndex",function e(r){var t=ToObject(this),n=ToLength(Get(t,"length"));if(!1===IsCallable(r))throw new TypeError(r+" is not a function");for(var o=arguments.length>1?arguments[1]:undefined,a=0;a<n;){var i=ToString(a),l=Get(t,i);if(ToBoolean(Call(r,o,[l,a,t])))return a;a+=1}return-1});}if (!("forEach"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"forEach",function r(t){var e=ToObject(this),n=e instanceof String?e.split(""):e,o=ToLength(Get(e,"length"));if(!1===IsCallable(t))throw new TypeError(t+" is not a function");for(var a=arguments.length>1?arguments[1]:undefined,i=0;i<o;){var f=ToString(i);if(HasProperty(n,f)){var l=Get(n,f);Call(t,a,[l,i,e])}i+=1}return undefined});}var _DOMTokenList=function(){var n=!0,t=function(t,e,r,o){Object.defineProperty?Object.defineProperty(t,e,{configurable:!1===n||!!o,get:r}):t.__defineGetter__(e,r)};try{t({},"support")}catch(e){n=!1}return function(n,e){var r=this,o=[],i={},a=0,c=0,f=function(n){t(r,n,function(){return u(),o[n]},!1)},l=function(){if(a>=c)for(;c<a;++c)f(c)},u=function(){var t,r,c=arguments,f=/\s+/;if(c.length)for(r=0;r<c.length;++r)if(f.test(c[r]))throw t=new SyntaxError('String "'+c[r]+'" contains an invalid character'),t.code=5,t.name="InvalidCharacterError",t;for(o="object"==typeof n[e]?(""+n[e].baseVal).replace(/^\s+|\s+$/g,"").split(f):(""+n[e]).replace(/^\s+|\s+$/g,"").split(f),""===o[0]&&(o=[]),i={},r=0;r<o.length;++r)i[o[r]]=!0;a=o.length,l()};return u(),t(r,"length",function(){return u(),a}),r.toLocaleString=r.toString=function(){return u(),o.join(" ")},r.item=function(n){return u(),o[n]},r.contains=function(n){return u(),!!i[n]},r.add=function(){u.apply(r,t=arguments);for(var t,c,f=0,p=t.length;f<p;++f)c=t[f],i[c]||(o.push(c),i[c]=!0);a!==o.length&&(a=o.length>>>0,"object"==typeof n[e]?n[e].baseVal=o.join(" "):n[e]=o.join(" "),l())},r.remove=function(){u.apply(r,t=arguments);for(var t,c={},f=0,p=[];f<t.length;++f)c[t[f]]=!0,delete i[t[f]];for(f=0;f<o.length;++f)c[o[f]]||p.push(o[f]);o=p,a=p.length>>>0,"object"==typeof n[e]?n[e].baseVal=o.join(" "):n[e]=o.join(" "),l()},r.toggle=function(n,t){return u.apply(r,[n]),undefined!==t?t?(r.add(n),!0):(r.remove(n),!1):i[n]?(r.remove(n),!1):(r.add(n),!0)},r.forEach=Array.prototype.forEach,r}}();if (!("DOMTokenList"in self&&function(e){return!("classList"in e)||!e.classList.toggle("x",!1)&&!e.className}(document.createElement("x"))
)) {!function(t){"DOMTokenList"in t&&t.DOMTokenList&&(!document.createElementNS||!document.createElementNS("http://www.w3.org/2000/svg","svg")||document.createElementNS("http://www.w3.org/2000/svg","svg").classList instanceof DOMTokenList)||(t.DOMTokenList=_DOMTokenList),function(){var t=document.createElement("span");"classList"in t&&(t.classList.toggle("x",!1),t.classList.contains("x")&&(t.classList.constructor.prototype.toggle=function s(t){var s=arguments[1];if(s===undefined){var e=!this.contains(t);return this[e?"add":"remove"](t),e}return s=!!s,this[s?"add":"remove"](t),s}))}(),function(){var t=document.createElement("span");if("classList"in t&&(t.classList.add("a","b"),!t.classList.contains("b"))){var s=t.classList.constructor.prototype.add;t.classList.constructor.prototype.add=function(){for(var t=arguments,e=arguments.length,n=0;n<e;n++)s.call(this,t[n])}}}(),function(){var t=document.createElement("span");if("classList"in t&&(t.classList.add("a"),t.classList.add("b"),t.classList.remove("a","b"),t.classList.contains("b"))){var s=t.classList.constructor.prototype.remove;t.classList.constructor.prototype.remove=function(){for(var t=arguments,e=arguments.length,n=0;n<e;n++)s.call(this,t[n])}}}()}(self);}if (!("includes"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"includes",function e(r){"use strict";var t=ToObject(this),o=ToLength(Get(t,"length"));if(0===o)return!1;var n=ToInteger(arguments[1]);if(n>=0)var a=n;else(a=o+n)<0&&(a=0);for(;a<o;){var i=Get(t,ToString(a));if(SameValueZero(r,i))return!0;a+=1}return!1});}if (!("indexOf"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"indexOf",function r(t){var e=ToObject(this),n=ToLength(Get(e,"length"));if(0===n)return-1;var i=ToInteger(arguments[1]);if(i>=n)return-1;if(i>=0)var o=1/i==-Infinity?0:i;else(o=n+i)<0&&(o=0);for(;o<n;){if(HasProperty(e,ToString(o))){if(t===Get(e,ToString(o)))return o}o+=1}return-1});}if (!("getOwnPropertyNames"in Object&&function(){try{return Object.getOwnPropertyNames(1),!0}catch(t){return!1}}()
)) {!function(){var t={}.toString,e="".split,r=[].concat,o=Object.prototype.hasOwnProperty,c=Object.getOwnPropertyNames||Object.keys,n="object"==typeof self?c(self):[];CreateMethodProperty(Object,"getOwnPropertyNames",function l(a){var p=ToObject(a);if("[object Window]"===t.call(p))try{return c(p)}catch(j){return r.call([],n)}p="[object String]"==t.call(p)?e.call(p,""):Object(p);for(var i=c(p),s=["length","prototype"],O=0;O<s.length;O++){var b=s[O];o.call(p,b)&&!i.includes(b)&&i.push(b)}if(i.includes("__proto__")){var f=i.indexOf("__proto__");i.splice(f,1)}return i})}();}if (!("lastIndexOf"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"lastIndexOf",function t(r){var e=ToObject(this),n=ToLength(Get(e,"length"));if(0===n)return-1;var o=arguments.length>1?ToInteger(arguments[1]):n-1;if(o>=0)var i=1/o==-Infinity?0:Math.min(o,n-1);else i=n+o;for(;i>=0;){if(HasProperty(e,ToString(i))){if(r===Get(e,ToString(i)))return i}i-=1}return-1});}if (!("reduce"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"reduce",function e(r){var t=ToObject(this),n=t instanceof String?t.split(""):t,o=ToLength(Get(n,"length"));if(!1===IsCallable(r))throw new TypeError(r+" is not a function");var i=arguments.length>1?arguments[1]:undefined;if(0===o&&arguments.length<2)throw new TypeError("Reduce of empty array with no initial value");var a=0,f=undefined;if(arguments.length>1)f=i;else{for(var l=!1;!1===l&&a<o;){var h=ToString(a);l=HasProperty(n,h),l&&(f=Get(n,h)),a+=1}if(!1===l)throw new TypeError("Reduce of empty array with no initial value")}for(;a<o;){if(h=ToString(a),l=HasProperty(n,h)){var y=Get(n,h);f=Call(r,undefined,[f,y,a,t])}a+=1}return f});}if (!("reduceRight"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"reduceRight",function e(r){var t=ToObject(this),n=t instanceof String?t.split(""):t,i=ToLength(Get(n,"length"));if(!1===IsCallable(r))throw new TypeError(r+" is not a function");var o=arguments.length>1?arguments[1]:undefined;if(0===i&&arguments.length<2)throw new TypeError("Reduce of empty array with no initial value");var a=i-1,f=undefined;if(arguments.length>1)f=o;else{for(var l=!1;!1===l&&a>=0;){var h=ToString(a);l=HasProperty(n,h),l&&(f=Get(n,h)),a-=1}if(!1===l)throw new TypeError("Reduce of empty array with no initial value")}for(;a>=0;){if(h=ToString(a),l=HasProperty(n,h)){var y=Get(n,h);f=Call(r,undefined,[f,y,a,t])}a-=1}return f});}if (!("some"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"some",function r(e){var t=ToObject(this),o=ToLength(Get(t,"length"));if(!1===IsCallable(e))throw new TypeError(e+" is not a function");for(var n=arguments.length>1?arguments[1]:undefined,a=0;a<o;){var i=ToString(a);if(HasProperty(t,i)){var l=Get(t,i);if(ToBoolean(Call(e,n,[l,a,t])))return!0}a+=1}return!1});}if (!("endsWith"in String.prototype
)) {CreateMethodProperty(String.prototype,"endsWith",function e(t){"use strict";var r=arguments.length>1?arguments[1]:undefined,n=RequireObjectCoercible(this),i=ToString(n);if(IsRegExp(t))throw new TypeError("First argument to String.prototype.endsWith must not be a regular expression");var o=ToString(t),s=i.length,g=r===undefined?s:ToInteger(r),h=Math.min(Math.max(g,0),s),u=o.length,a=h-u;return!(a<0)&&i.substr(a,u)===o});}if (!("includes"in String.prototype
)) {CreateMethodProperty(String.prototype,"includes",function e(t){"use strict";var r=arguments.length>1?arguments[1]:undefined,n=RequireObjectCoercible(this),i=ToString(n);if(IsRegExp(t))throw new TypeError("First argument to String.prototype.includes must not be a regular expression");var o=ToString(t),g=ToInteger(r),a=i.length,p=Math.min(Math.max(g,0),a);return-1!==String.prototype.indexOf.call(i,o,p)});}if (!("repeat"in String.prototype
)) {CreateMethodProperty(String.prototype,"repeat",function r(e){"use strict";var t=RequireObjectCoercible(this),n=ToString(t),o=ToInteger(e);if(o<0)throw new RangeError("Invalid count value");if(o===Infinity)throw new RangeError("Invalid count value");return 0===o?"":new Array(o+1).join(n)});}if (!("startsWith"in String.prototype
)) {CreateMethodProperty(String.prototype,"startsWith",function t(e){"use strict";var r=arguments.length>1?arguments[1]:undefined,n=RequireObjectCoercible(this),i=ToString(n);if(IsRegExp(e))throw new TypeError("First argument to String.prototype.startsWith must not be a regular expression");var o=ToString(e),s=ToInteger(r),a=i.length,g=Math.min(Math.max(s,0),a);return!(o.length+g>a)&&0===i.substr(g).indexOf(e)});}function ToPropertyKey(r){var i=ToPrimitive(r,String);return"symbol"===Type(i)?i:ToString(i)}if (!("getOwnPropertyDescriptor"in Object&&"function"==typeof Object.getOwnPropertyDescriptor&&function(){try{return"3"===Object.getOwnPropertyDescriptor("13.7",1).value}catch(t){return!1}}()
)) {!function(){var e=Object.getOwnPropertyDescriptor,t=function(){try{return 1===Object.defineProperty(document.createElement("div"),"one",{get:function(){return 1}}).one}catch(e){return!1}},r={}.toString,n="".split;CreateMethodProperty(Object,"getOwnPropertyDescriptor",function c(o,i){var a=ToObject(o);a=("string"===Type(a)||a instanceof String)&&"[object String]"==r.call(o)?n.call(o,""):Object(o);var u=ToPropertyKey(i);if(t)try{return e(a,u)}catch(l){}if(HasOwnProperty(a,u))return{enumerable:!0,configurable:!0,writable:!0,value:a[u]}})}();}if (!((function(){if(!("HTMLInputElement"in self&&"valueAsDate"in HTMLInputElement.prototype))return!1
try{for(var e=document.createElement("INPUT"),t=[["date","2006-01-02",new Date("2006-01-02T00:00:00.000Z")],["month","2019-12",new Date("2019-12-01T00:00:00.000Z")],["week","2015-W53",new Date("2015-12-28T00:00:00.000Z")],["time","21:59",new Date("1970-01-01T21:59:00.000Z")]],n=0;n<t.length;n++){var r=t[n]
try{e.type=r[0]}catch(a){}if(e.setAttribute("type",r[0]),e.value=r[1],e.valueAsDate.getTime()!==r[2].getTime())return!1}}catch(u){return!1}return!0})()
)) {!function(e){function t(e){return e%400==0||e%4==0&&e%100!=0}function r(e,t){switch(e){case 1:return 31;case 2:return t?29:28;case 3:return 31;case 4:return 30;case 5:return 31;case 6:return 30;case 7:case 8:return 31;case 9:return 30;case 10:return 31;case 11:return 30;case 12:return 31;default:return-1}}function u(e,r,u){var a=[0,31,59,90,120,151,181,212,243,273,304,334];return t(e)&&a[2]++,a[r]+u}if("HTMLInputElement"in e){var a=/^\d{4,}-[0-1][0-9]-[0-3][0-9]$/,l=/^\d{4,}-[0-1][0-9]$/,n=/^\d{4,}-W[0-5][0-9]$/,i=/^[0-2][0-9]:[0-5][0-9]$/,s=Object.getOwnPropertyDescriptor(HTMLInputElement.prototype,"valueAsDate");s&&!s.configurable||e.Object.defineProperty(e.HTMLInputElement.prototype,"valueAsDate",{enumerable:!0,configurable:!0,get:function(){if(""===this.value)return null;if(s&&s.get)try{var e=s.get.apply(this);if(e instanceof Date)return e}catch(A){}var u=this.type;"text"===u&&(u=this.getAttribute("type"));try{switch(u){case"date":if(!a.test(this.value))return null;var c=this.value.split("-"),v=Number(c[0]),h=Number(c[1]),T=Number(c[2]);if(0===v||0===h||0===T)return null;if(h>12)return null;if(T>r(h,t(v)))return null;var C=new Date(Date.UTC(v,h-1,T));return C.setUTCFullYear(v),C;case"month":if(!l.test(this.value))return null;var U=this.value.split("-"),o=Number(U[0]),f=Number(U[1]);if(0===o||0===f)return null;if(f>12)return null;var D=new Date(Date.UTC(o,f-1));return D.setUTCFullYear(o),D;case"week":if(!n.test(this.value))return null;var g=this.value.split("-"),p=Number(g[0]),y=Number(g[1].slice(1));if(0===p||0===y)return null;if(y>53)return null;var d=!1,m=new Date(Date.UTC(p,0,1,0));if(m.setUTCFullYear(p),m.setUTCMonth(0),m.setUTCDate(1),4===m.getUTCDay()?d=!0:t(p)&&3===m.getUTCDay()&&(d=!0),!d&&y>52)return null;var b=new Date(Date.UTC(p,0,1+7*(y-1)));b.setUTCFullYear(p);var w=b.getUTCDay(),M=new Date(b);return w<=4?M.setUTCDate(b.getUTCDate()-b.getUTCDay()+1):M.setUTCDate(b.getUTCDate()+8-b.getUTCDay()),M;case"time":if(!i.test(this.value))return null;var F=this.value.split(":"),N=Number(F[0]),Y=Number(F[1]);return N>23?null:Y>59?null:new Date(Date.UTC(1970,0,1,N,Y));default:return null}}catch(A){return null}},set:function(e){var t=this.type;switch("text"===t&&(t=this.getAttribute("type")),t){case"date":if(null===e)return void(this.value="");try{var r=e.getUTCFullYear(),a=("0"+(e.getUTCMonth()+1)).slice(-2),l=("0"+e.getUTCDate()).slice(-2);return r<1e3&&(r=("0000"+r).slice(-4)),void(this.value=r+"-"+a+"-"+l)}catch(U){return void(this.value="")}case"month":if(null===e)return void(this.value="");try{var n=e.getUTCFullYear(),i=("0"+(e.getUTCMonth()+1)).slice(-2);return n<1e3&&(n=("0000"+n).slice(-4)),void(this.value=n+"-"+i)}catch(U){return void(this.value="")}case"week":if(null===e)return void(this.value="");try{var c=new Date(Date.UTC(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()));c.setUTCDate(e.getUTCDate()+4-(e.getUTCDay()||7));var v=Math.ceil(u(c.getUTCFullYear(),c.getUTCMonth(),c.getUTCDate())/7),h=c.getUTCFullYear();return h<1e3&&(h=("0000"+h).slice(-4)),void(this.value=h+"-W"+("0"+v).slice(-2))}catch(U){return void(this.value="")}case"time":if(null===e)return void(this.value="");try{var T=("0"+e.getUTCHours()).slice(-2),C=("0"+e.getUTCMinutes()).slice(-2);return void(this.value=T+":"+C)}catch(U){return void(this.value="")}default:s&&s.set&&s.set.apply(this,e)}}})}}(self);}if (!("assign"in Object
)) {CreateMethodProperty(Object,"assign",function e(t,r){var n=ToObject(t);if(1===arguments.length)return n;var o,c,a,l,i=Array.prototype.slice.call(arguments,1);for(o=0;o<i.length;o++){var p=i[o];for(p===undefined||null===p?a=[]:(l="[object String]"===Object.prototype.toString.call(p)?String(p).split(""):ToObject(p),a=Object.keys(l)),c=0;c<a.length;c++){var b,y=a[c];try{var g=Object.getOwnPropertyDescriptor(l,y);b=g!==undefined&&!0===g.enumerable}catch(u){b=Object.prototype.propertyIsEnumerable.call(l,y)}if(b){var j=Get(l,y);n[y]=j}}}return n});}if (!("defineProperties"in Object
)) {CreateMethodProperty(Object,"defineProperties",function e(r,t){if("object"!==Type(r))throw new TypeError("Object.defineProperties called on non-object");for(var o=ToObject(t),n=Object.keys(o),c=[],i=0;i<n.length;i++){var b=n[i],f=Object.getOwnPropertyDescriptor(o,b);if(f!==undefined&&f.enumerable){var p=Get(o,b),a=p;c.push([b,a])}}for(var j=0;j<c.length;j++){var d=c[j][0];a=c[j][1],Object.defineProperty(r,d,a)}return r});}if (!("create"in Object
)) {!function(){function e(){}if({__proto__:null}instanceof Object)t=function(){var e=document.createElement("iframe");e.style.display="none";var o=document.body||document.documentElement;o.appendChild(e),e.src="javascript:";var n=e.contentWindow.Object.prototype;o.removeChild(e),e=null,delete n.constructor,delete n.hasOwnProperty,delete n.propertyIsEnumerable,delete n.isPrototypeOf,delete n.toLocaleString,delete n.toString,delete n.valueOf;var r=function l(){};return r.prototype=n,t=function(){return new r},new r};else var t=function(){return{__proto__:null}};CreateMethodProperty(Object,"create",function o(n,r){if("object"!==Type(n)&&"null"!==Type(n))throw new TypeError("Object prototype may only be an Object or null");if("null"===Type(n))var l=t();else e.prototype=n,l=new e,l.__proto__=n,l.constructor.prototype=n,l.__proto__=n;return 1 in arguments?Object.defineProperties(l,r):l})}();}if (!("DocumentFragment"in self&&function(){try{return new DocumentFragment,!0}catch(n){return!1}}()
)) {!function(t){t.DocumentFragment=function n(){return document.createDocumentFragment()};var e=document.createDocumentFragment();t.DocumentFragment.prototype=Object.create(e.constructor.prototype)}(self);}function OrdinaryCreateFromConstructor(r,e){var t=arguments[2]||{},o=GetPrototypeFromConstructor(r,e),a=Object.create(o);for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&Object.defineProperty(a,n,{configurable:!0,enumerable:!1,writable:!0,value:t[n]});return a}function Construct(r){var t=arguments.length>2?arguments[2]:r,o=arguments.length>1?arguments[1]:[];if(!IsConstructor(r))throw new TypeError("F must be a constructor.");if(!IsConstructor(t))throw new TypeError("newTarget must be a constructor.");if(t===r)return new(Function.prototype.bind.apply(r,[null].concat(o)));var n=OrdinaryCreateFromConstructor(t,Object.prototype);return Call(r,n,o)}if (!("of"in Array
)) {CreateMethodProperty(Array,"of",function r(){var r=arguments.length,t=arguments,e=this;if(IsConstructor(e))var a=Construct(e,[r]);else a=ArrayCreate(r);for(var o=0;o<r;){var n=t[o],h=ToString(o);CreateDataPropertyOrThrow(a,h,n),o+=1}return a.length=r,a});}function ArraySpeciesCreate(e,r){if(0===r&&1/r==-Infinity&&(r=0),!1===IsArray(e))return ArrayCreate(r);var n=Get(e,"constructor");if("object"===Type(n)&&null===(n="Symbol"in self&&"species"in self.Symbol?Get(n,self.Symbol.species):undefined)&&(n=undefined),n===undefined)return ArrayCreate(r);if(!IsConstructor(n))throw new TypeError("C must be a constructor");return Construct(n,[r])}if (!("filter"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"filter",function r(e){var t=ToObject(this),o=ToLength(Get(t,"length"));if(!1===IsCallable(e))throw new TypeError(e+" is not a function");for(var a=arguments.length>1?arguments[1]:undefined,n=ArraySpeciesCreate(t,0),i=0,l=0;i<o;){var f=ToString(i);if(HasProperty(t,f)){var h=Get(t,f);ToBoolean(Call(e,a,[h,i,t]))&&(CreateDataPropertyOrThrow(n,ToString(l),h),l+=1)}i+=1}return n});}if (!("map"in Array.prototype
)) {CreateMethodProperty(Array.prototype,"map",function r(e){var t=ToObject(this),a=ToLength(Get(t,"length"));if(!1===IsCallable(e))throw new TypeError(e+" is not a function");for(var o=arguments.length>1?arguments[1]:undefined,n=ArraySpeciesCreate(t,a),i=0;i<a;){var p=ToString(i);if(HasProperty(t,p)){var h=Get(t,p),l=Call(e,o,[h,i,t]);CreateDataPropertyOrThrow(n,p,l)}i+=1}return n});}if (!("sort"in Array.prototype&&function(){var r={length:3,0:2,1:1,2:3}
return Array.prototype.sort.call(r,function(r,t){return r-t})===r}()
)) {"use strict";var origSort=Array.prototype.sort;CreateMethodProperty(Array.prototype,"sort",function r(t){if(t!==undefined&&!1===IsCallable(t))throw new TypeError("The comparison function must be either a function or undefined");if(t===undefined)origSort.call(this);else{var e=Array.prototype.map.call(this,function(r,t){return{item:r,index:t}});origSort.call(e,function(r,e){var i=t.call(undefined,r.item,e.item);return 0===i?r.index-e.index:i});for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&e[i].item!==this[i]&&(this[i]=e[i].item)}return this});}if (!("setPrototypeOf"in Object
)) {!function(){if(!Object.setPrototypeOf){var t,e,o=Object.getOwnPropertyNames,r=Object.getOwnPropertyDescriptor,n=Object.create,c=Object.defineProperty,_=Object.getPrototypeOf,f=Object.prototype,p=function(t,e){return o(e).forEach(function(o){c(t,o,r(e,o))}),t},O=function i(t,e){return p(n(e),t)};try{t=r(f,"__proto__").set,t.call({},null),e=function a(e,o){return t.call(e,o),e}}catch(u){t={__proto__:null},t instanceof Object?e=O:(t.__proto__=f,e=t instanceof Object?function o(t,e){return t.__proto__=e,t}:function r(t,e){return _(t)?(t.__proto__=e,t):O(t,e)})}CreateMethodProperty(Object,"setPrototypeOf",e)}}();}if (!("Symbol"in self&&0===self.Symbol.length
)) {!function(e,r,n){"use strict";function t(e){if("symbol"===Type(e))return e;throw TypeError(e+" is not a symbol")}var u,o=function(){try{var r={};return e.defineProperty(r,"t",{configurable:!0,enumerable:!1,get:function(){return!0},set:undefined}),!!r.t}catch(n){return!1}}(),i=0,a=""+Math.random(),c="__symbol:",l=c.length,f="__symbol@@"+a,s={},v="defineProperty",y="defineProperties",b="getOwnPropertyNames",p="getOwnPropertyDescriptor",h="propertyIsEnumerable",m=e.prototype,d=m.hasOwnProperty,g=m[h],w=m.toString,S=Array.prototype.concat,P=e.getOwnPropertyNames?e.getOwnPropertyNames(self):[],O=e[b],j=function $(e){if("[object Window]"===w.call(e))try{return O(e)}catch(r){return S.call([],P)}return O(e)},E=e[p],N=e.create,T=e.keys,_=e.freeze||e,k=e[v],F=e[y],I=E(e,b),x=function(e,r,n){if(!d.call(e,f))try{k(e,f,{enumerable:!1,configurable:!1,writable:!1,value:{}})}catch(t){e[f]={}}e[f]["@@"+r]=n},z=function(e,r){var n=N(e);return j(r).forEach(function(e){q.call(r,e)&&L(n,e,r[e])}),n},A=function(e){var r=N(e);return r.enumerable=!1,r},D=function ee(){},M=function(e){return e!=f&&!d.call(H,e)},W=function(e){return e!=f&&d.call(H,e)},q=function re(e){var r=""+e;return W(r)?d.call(this,r)&&this[f]&&this[f]["@@"+r]:g.call(this,e)},B=function(r){var n={enumerable:!1,configurable:!0,get:D,set:function(e){u(this,r,{enumerable:!1,configurable:!0,writable:!0,value:e}),x(this,r,!0)}};try{k(m,r,n)}catch(o){m[r]=n.value}H[r]=k(e(r),"constructor",J);var t=E(G.prototype,"description");return t&&k(H[r],"description",t),_(H[r])},C=function(e){var r=t(e);if(Y){var n=V(r);if(""!==n)return n.slice(1,-1)}if(s[r]!==undefined)return s[r];var u=r.toString(),o=u.lastIndexOf("0.");return u=u.slice(10,o),""===u?undefined:u},G=function ne(){var r=arguments[0];if(this instanceof ne)throw new TypeError("Symbol is not a constructor");var n=c.concat(r||"",a,++i);r===undefined||null!==r&&!isNaN(r)&&""!==String(r)||(s[n]=String(r));var t=B(n);return o||e.defineProperty(t,"description",{configurable:!0,enumerable:!1,value:C(t)}),t},H=N(null),J={value:G},K=function(e){return H[e]},L=function te(e,r,n){var t=""+r;return W(t)?(u(e,t,n.enumerable?A(n):n),x(e,t,!!n.enumerable)):k(e,r,n),e},Q=function(e){return function(r){return d.call(e,f)&&d.call(e[f],"@@"+r)}},R=function ue(e){return j(e).filter(e===m?Q(e):W).map(K)};I.value=L,k(e,v,I),I.value=R,k(e,"getOwnPropertySymbols",I),I.value=function oe(e){return j(e).filter(M)},k(e,b,I),I.value=function ie(e,r){var n=R(r);return n.length?T(r).concat(n).forEach(function(n){q.call(r,n)&&L(e,n,r[n])}):F(e,r),e},k(e,y,I),I.value=q,k(m,h,I),I.value=G,k(n,"Symbol",I),I.value=function(e){var r=c.concat(c,e,a);return r in m?H[r]:B(r)},k(G,"for",I),I.value=function(e){if(M(e))throw new TypeError(e+" is not a symbol");return d.call(H,e)?e.slice(2*l,-a.length):void 0},k(G,"keyFor",I),I.value=function ae(e,r){var n=E(e,r);return n&&W(r)&&(n.enumerable=q.call(e,r)),n},k(e,p,I),I.value=function ce(e,r){return 1===arguments.length||void 0===r?N(e):z(e,r)},k(e,"create",I);var U=null===function(){return this}.call(null);if(I.value=U?function(){var e=w.call(this);return"[object String]"===e&&W(this)?"[object Symbol]":e}:function(){if(this===window)return"[object Null]";var e=w.call(this);return"[object String]"===e&&W(this)?"[object Symbol]":e},k(m,"toString",I),u=function(e,r,n){var t=E(m,r);delete m[r],k(e,r,n),e!==m&&k(m,r,t)},function(){try{var r={};return e.defineProperty(r,"t",{configurable:!0,enumerable:!1,get:function(){return!0},set:undefined}),!!r.t}catch(n){return!1}}()){var V;try{V=Function("s","var v = s.valueOf(); return { [v]() {} }[v].name;")}catch(Z){}var X=function(){},Y=V&&"inferred"===X.name?V:null;e.defineProperty(n.Symbol.prototype,"description",{configurable:!0,enumerable:!1,get:function(){return C(this)}})}}(Object,0,self);}if (!("Symbol"in self&&"hasInstance"in self.Symbol
)) {Object.defineProperty(Symbol,"hasInstance",{value:Symbol("hasInstance")});}if (!("Symbol"in self&&"isConcatSpreadable"in self.Symbol
)) {Object.defineProperty(Symbol,"isConcatSpreadable",{value:Symbol("isConcatSpreadable")});}if (!("Symbol"in self&&"iterator"in self.Symbol
)) {Object.defineProperty(self.Symbol,"iterator",{value:self.Symbol("iterator")});}function GetIterator(t){var e=arguments.length>1?arguments[1]:GetMethod(t,Symbol.iterator),r=Call(e,t);if("object"!==Type(r))throw new TypeError("bad iterator");var o=GetV(r,"next"),a=Object.create(null);return a["[[Iterator]]"]=r,a["[[NextMethod]]"]=o,a["[[Done]]"]=!1,a}function IterableToList(t){for(var r=arguments.length>1?GetIterator(t,arguments[1]):GetIterator(t),e=[],a=!0;!1!==a;)if(!1!==(a=IteratorStep(r))){var o=IteratorValue(a);e.push(o)}return e}if (!("AggregateError"in self
)) {!function(){function r(r,e){var t=void 0===e?new Error:new Error(e);CreateDataPropertyOrThrow(this,"name","AggregateError"),CreateDataPropertyOrThrow(this,"message",t.message),CreateDataPropertyOrThrow(this,"stack",t.stack);var o;if(Array.isArray(r))o=r.slice();else try{o=IterableToList(r)}catch(a){throw new TypeError("Argument is not iterable")}CreateDataPropertyOrThrow(this,"errors",o)}r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,self.AggregateError=r}();}if (!("Symbol"in self&&"match"in self.Symbol
)) {Object.defineProperty(Symbol,"match",{value:Symbol("match")});}if (!("Symbol"in self&&"matchAll"in self.Symbol
)) {Object.defineProperty(Symbol,"matchAll",{value:Symbol("matchAll")});}if (!("Symbol"in self&&"replace"in self.Symbol
)) {Object.defineProperty(Symbol,"replace",{value:Symbol("replace")});}if (!("Symbol"in self&&"search"in self.Symbol
)) {Object.defineProperty(Symbol,"search",{value:Symbol("search")});}if (!("Symbol"in self&&"species"in self.Symbol
)) {Object.defineProperty(Symbol,"species",{value:Symbol("species")});}if (!("Map"in self&&function(t){try{var n=new t.Map([[1,1],[2,2]])
return 0===t.Map.length&&2===n.size&&"Symbol"in t&&"iterator"in t.Symbol&&"function"==typeof n[t.Symbol.iterator]}catch(e){return!1}}(self)
)) {!function(e){function t(e,t){if("object"!==Type(e))throw new TypeError("createMapIterator called on incompatible receiver "+Object.prototype.toString.call(e));if(!0!==e._es6Map)throw new TypeError("createMapIterator called on incompatible receiver "+Object.prototype.toString.call(e));var r=Object.create(u);return Object.defineProperty(r,"[[Map]]",{configurable:!0,enumerable:!1,writable:!0,value:e}),Object.defineProperty(r,"[[MapNextIndex]]",{configurable:!0,enumerable:!1,writable:!0,value:0}),Object.defineProperty(r,"[[MapIterationKind]]",{configurable:!0,enumerable:!1,writable:!0,value:t}),r}var r=function(){try{var e={};return Object.defineProperty(e,"t",{configurable:!0,enumerable:!1,get:function(){return!0},set:undefined}),!!e.t}catch(t){return!1}}(),o=0,a=Symbol("meta_"+(1e8*Math.random()+"").replace(".","")),n=function(e){if("object"==typeof e?null!==e:"function"==typeof e){if(!Object.isExtensible(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,a)){var t=typeof e+"-"+ ++o;Object.defineProperty(e,a,{configurable:!1,enumerable:!1,writable:!1,value:t})}return e[a]}return""+e},i=function(e,t){var r=n(t);if(!1===r)return p(e,t);var o=e._table[r];return o!==undefined&&o},p=function(e,t){for(var r=0;r<e._keys.length;r++){var o=e._keys[r];if(o!==c&&SameValueZero(o,t))return r}return!1},l=function(e,t,r){var o=n(t);return!1!==o&&(!1===r?delete e._table[o]:e._table[o]=r,!0)},c=Symbol("undef"),y=function f(){if(!(this instanceof f))throw new TypeError('Constructor Map requires "new"');var e=OrdinaryCreateFromConstructor(this,f.prototype,{_table:{},_keys:[],_values:[],_size:0,_es6Map:!0});r||Object.defineProperty(e,"size",{configurable:!0,enumerable:!1,writable:!0,value:0});var t=arguments.length>0?arguments[0]:undefined;if(null===t||t===undefined)return e;var o=e.set;if(!IsCallable(o))throw new TypeError("Map.prototype.set is not a function");try{for(var a=GetIterator(t);;){var n=IteratorStep(a);if(!1===n)return e;var i=IteratorValue(n);if("object"!==Type(i))try{throw new TypeError("Iterator value "+i+" is not an entry object")}catch(u){return IteratorClose(a,u)}try{var p=i[0],l=i[1];o.call(e,p,l)}catch(s){return IteratorClose(a,s)}}}catch(s){if(Array.isArray(t)||"[object Arguments]"===Object.prototype.toString.call(t)||t.callee){var c,y=t.length;for(c=0;c<y;c++)o.call(e,t[c][0],t[c][1])}}return e};Object.defineProperty(y,"prototype",{configurable:!1,enumerable:!1,writable:!1,value:{}}),r?Object.defineProperty(y,Symbol.species,{configurable:!0,enumerable:!1,get:function(){return this},set:undefined}):CreateMethodProperty(y,Symbol.species,y),CreateMethodProperty(y.prototype,"clear",function b(){var e=this;if("object"!==Type(e))throw new TypeError("Method Map.prototype.clear called on incompatible receiver "+Object.prototype.toString.call(e));if(!0!==e._es6Map)throw new TypeError("Method Map.prototype.clear called on incompatible receiver "+Object.prototype.toString.call(e));for(var t=e._keys,o=0;o<t.length;o++)e._keys[o]=c,e._values[o]=c;return this._size=0,r||(this.size=this._size),this._table={},undefined}),CreateMethodProperty(y.prototype,"constructor",y),CreateMethodProperty(y.prototype,"delete",function(e){var t=this;if("object"!==Type(t))throw new TypeError("Method Map.prototype.clear called on incompatible receiver "+Object.prototype.toString.call(t));if(!0!==t._es6Map)throw new TypeError("Method Map.prototype.clear called on incompatible receiver "+Object.prototype.toString.call(t));var o=i(t,e);if(!1!==o){var a=t._keys[o];if(a!==c&&SameValueZero(a,e))return this._keys[o]=c,this._values[o]=c,this._size=--this._size,r||(this.size=this._size),l(this,e,!1),!0}return!1}),CreateMethodProperty(y.prototype,"entries",function h(){return t(this,"key+value")}),CreateMethodProperty(y.prototype,"forEach",function(e){var t=this;if("object"!==Type(t))throw new TypeError("Method Map.prototype.forEach called on incompatible receiver "+Object.prototype.toString.call(t));if(!0!==t._es6Map)throw new TypeError("Method Map.prototype.forEach called on incompatible receiver "+Object.prototype.toString.call(t));if(!IsCallable(e))throw new TypeError(Object.prototype.toString.call(e)+" is not a function.");if(arguments[1])var r=arguments[1];for(var o=t._keys,a=0;a<o.length;a++)t._keys[a]!==c&&t._values[a]!==c&&e.call(r,t._values[a],t._keys[a],t);return undefined}),CreateMethodProperty(y.prototype,"get",function d(e){var t=this;if("object"!==Type(t))throw new TypeError("Method Map.prototype.get called on incompatible receiver "+Object.prototype.toString.call(t));if(!0!==t._es6Map)throw new TypeError("Method Map.prototype.get called on incompatible receiver "+Object.prototype.toString.call(t));var r=i(t,e);if(!1!==r){var o=t._keys[r];if(o!==c&&SameValueZero(o,e))return t._values[r]}return undefined}),CreateMethodProperty(y.prototype,"has",function v(e){var t=this;if("object"!=typeof t)throw new TypeError("Method Map.prototype.has called on incompatible receiver "+Object.prototype.toString.call(t));if(!0!==t._es6Map)throw new TypeError("Method Map.prototype.has called on incompatible receiver "+Object.prototype.toString.call(t));var r=i(t,e);if(!1!==r){var o=t._keys[r];if(o!==c&&SameValueZero(o,e))return!0}return!1}),CreateMethodProperty(y.prototype,"keys",function M(){return t(this,"key")}),CreateMethodProperty(y.prototype,"set",function w(e,t){var o=this;if("object"!==Type(o))throw new TypeError("Method Map.prototype.set called on incompatible receiver "+Object.prototype.toString.call(o));if(!0!==o._es6Map)throw new TypeError("Method Map.prototype.set called on incompatible receiver "+Object.prototype.toString.call(o));var a=i(o,e);if(!1!==a)o._values[a]=t;else{-0===e&&(e=0);var n={"[[Key]]":e,"[[Value]]":t};o._keys.push(n["[[Key]]"]),o._values.push(n["[[Value]]"]),l(o,e,o._keys.length-1),++o._size,r||(o.size=o._size)}return o}),r&&Object.defineProperty(y.prototype,"size",{configurable:!0,enumerable:!1,get:function(){var e=this;if("object"!==Type(e))throw new TypeError("Method Map.prototype.size called on incompatible receiver "+Object.prototype.toString.call(e));if(!0!==e._es6Map)throw new TypeError("Method Map.prototype.size called on incompatible receiver "+Object.prototype.toString.call(e));return this._size},set:undefined}),CreateMethodProperty(y.prototype,"values",function j(){return t(this,"value")}),CreateMethodProperty(y.prototype,Symbol.iterator,y.prototype.entries),"name"in y||Object.defineProperty(y,"name",{configurable:!0,enumerable:!1,writable:!1,value:"Map"});var u={};Object.defineProperty(u,"isMapIterator",{configurable:!1,enumerable:!1,writable:!1,value:!0}),CreateMethodProperty(u,"next",function _(){var e=this;if("object"!==Type(e))throw new TypeError("Method %MapIteratorPrototype%.next called on incompatible receiver "+Object.prototype.toString.call(e));if(!e.isMapIterator)throw new TypeError("Method %MapIteratorPrototype%.next called on incompatible receiver "+Object.prototype.toString.call(e));var t=e["[[Map]]"],r=e["[[MapNextIndex]]"],o=e["[[MapIterationKind]]"];if(t===undefined)return CreateIterResultObject(undefined,!0);if(!t._es6Map)throw new Error(Object.prototype.toString.call(t)+" has a [[MapData]] internal slot.");for(var a=t._keys,n=a.length;r<n;){var i=Object.create(null);if(i["[[Key]]"]=t._keys[r],i["[[Value]]"]=t._values[r],r+=1,e["[[MapNextIndex]]"]=r,i["[[Key]]"]!==c){if("key"===o)var p=i["[[Key]]"];else if("value"===o)p=i["[[Value]]"];else{if("key+value"!==o)throw new Error;p=[i["[[Key]]"],i["[[Value]]"]]}return CreateIterResultObject(p,!1)}}return e["[[Map]]"]=undefined,CreateIterResultObject(undefined,!0)}),CreateMethodProperty(u,Symbol.iterator,function g(){return this});try{CreateMethodProperty(e,"Map",y)}catch(s){e.Map=y}}(self);}if (!("Set"in self&&function(){try{var e=new self.Set([1,2])
return 0===self.Set.length&&2===e.size&&"Symbol"in self&&"iterator"in self.Symbol&&"function"==typeof e[self.Symbol.iterator]}catch(t){return!1}}()
)) {!function(e){function t(e,t){if("object"!=typeof e)throw new TypeError("createSetIterator called on incompatible receiver "+Object.prototype.toString.call(e));if(!0!==e._es6Set)throw new TypeError("createSetIterator called on incompatible receiver "+Object.prototype.toString.call(e));var r=Object.create(i);return Object.defineProperty(r,"[[IteratedSet]]",{configurable:!0,enumerable:!1,writable:!0,value:e}),Object.defineProperty(r,"[[SetNextIndex]]",{configurable:!0,enumerable:!1,writable:!0,value:0}),Object.defineProperty(r,"[[SetIterationKind]]",{configurable:!0,enumerable:!1,writable:!0,value:t}),r}var r=function(){try{var e={};return Object.defineProperty(e,"t",{configurable:!0,enumerable:!1,get:function(){return!0},set:undefined}),!!e.t}catch(t){return!1}}(),o=Symbol("undef"),n=function c(){if(!(this instanceof c))throw new TypeError('Constructor Set requires "new"');var e=OrdinaryCreateFromConstructor(this,c.prototype,{_values:[],_size:0,_es6Set:!0});r||Object.defineProperty(e,"size",{configurable:!0,enumerable:!1,writable:!0,value:0});var t=arguments.length>0?arguments[0]:undefined;if(null===t||t===undefined)return e;var o=e.add;if(!IsCallable(o))throw new TypeError("Set.prototype.add is not a function");try{for(var n=GetIterator(t);;){var a=IteratorStep(n);if(!1===a)return e;var i=IteratorValue(a);try{o.call(e,i)}catch(y){return IteratorClose(n,y)}}}catch(y){if(!Array.isArray(t)&&"[object Arguments]"!==Object.prototype.toString.call(t)&&!t.callee)throw y;var l,p=t.length;for(l=0;l<p;l++)o.call(e,t[l])}return e};Object.defineProperty(n,"prototype",{configurable:!1,enumerable:!1,writable:!1,value:{}}),r?Object.defineProperty(n,Symbol.species,{configurable:!0,enumerable:!1,get:function(){return this},set:undefined}):CreateMethodProperty(n,Symbol.species,n),CreateMethodProperty(n.prototype,"add",function p(e){var t=this;if("object"!=typeof t)throw new TypeError("Method Set.prototype.add called on incompatible receiver "+Object.prototype.toString.call(t));if(!0!==t._es6Set)throw new TypeError("Method Set.prototype.add called on incompatible receiver "+Object.prototype.toString.call(t));for(var n=t._values,a=0;a<n.length;a++){var i=n[a];if(i!==o&&SameValueZero(i,e))return t}return 0===e&&1/e==-Infinity&&(e=0),t._values.push(e),this._size=++this._size,r||(this.size=this._size),t}),CreateMethodProperty(n.prototype,"clear",function y(){var e=this;if("object"!=typeof e)throw new TypeError("Method Set.prototype.clear called on incompatible receiver "+Object.prototype.toString.call(e));if(!0!==e._es6Set)throw new TypeError("Method Set.prototype.clear called on incompatible receiver "+Object.prototype.toString.call(e));for(var t=e._values,n=0;n<t.length;n++)t[n]=o;return this._size=0,r||(this.size=this._size),undefined}),CreateMethodProperty(n.prototype,"constructor",n),CreateMethodProperty(n.prototype,"delete",function(e){var t=this;if("object"!=typeof t)throw new TypeError("Method Set.prototype.delete called on incompatible receiver "+Object.prototype.toString.call(t));if(!0!==t._es6Set)throw new TypeError("Method Set.prototype.delete called on incompatible receiver "+Object.prototype.toString.call(t));for(var n=t._values,a=0;a<n.length;a++){var i=n[a];if(i!==o&&SameValueZero(i,e))return n[a]=o,this._size=--this._size,r||(this.size=this._size),!0}return!1}),CreateMethodProperty(n.prototype,"entries",function u(){return t(this,"key+value")}),CreateMethodProperty(n.prototype,"forEach",function f(e){var t=this;if("object"!=typeof t)throw new TypeError("Method Set.prototype.forEach called on incompatible receiver "+Object.prototype.toString.call(t));if(!0!==t._es6Set)throw new TypeError("Method Set.prototype.forEach called on incompatible receiver "+Object.prototype.toString.call(t));if(!IsCallable(e))throw new TypeError(Object.prototype.toString.call(e)+" is not a function.");if(arguments[1])var r=arguments[1];for(var n=t._values,a=0;a<n.length;a++){var i=n[a];i!==o&&e.call(r,i,i,t)}return undefined}),CreateMethodProperty(n.prototype,"has",function d(e){var t=this;if("object"!=typeof t)throw new TypeError("Method Set.prototype.forEach called on incompatible receiver "+Object.prototype.toString.call(t));if(!0!==t._es6Set)throw new TypeError("Method Set.prototype.forEach called on incompatible receiver "+Object.prototype.toString.call(t));for(var r=t._values,n=0;n<r.length;n++){var a=r[n];if(a!==o&&SameValueZero(a,e))return!0}return!1});var a=function h(){return t(this,"value")};CreateMethodProperty(n.prototype,"values",a),CreateMethodProperty(n.prototype,"keys",a),r&&Object.defineProperty(n.prototype,"size",{configurable:!0,enumerable:!1,get:function(){var e=this;if("object"!=typeof e)throw new TypeError("Method Set.prototype.size called on incompatible receiver "+Object.prototype.toString.call(e));if(!0!==e._es6Set)throw new TypeError("Method Set.prototype.size called on incompatible receiver "+Object.prototype.toString.call(e));for(var t=e._values,r=0,n=0;n<t.length;n++){t[n]!==o&&(r+=1)}return r},set:undefined}),CreateMethodProperty(n.prototype,Symbol.iterator,a),"name"in n||Object.defineProperty(n,"name",{configurable:!0,enumerable:!1,writable:!1,value:"Set"});var i={};Object.defineProperty(i,"isSetIterator",{configurable:!1,enumerable:!1,writable:!1,value:!0}),CreateMethodProperty(i,"next",function b(){var e=this;if("object"!=typeof e)throw new TypeError("Method %SetIteratorPrototype%.next called on incompatible receiver "+Object.prototype.toString.call(e));if(!e.isSetIterator)throw new TypeError("Method %SetIteratorPrototype%.next called on incompatible receiver "+Object.prototype.toString.call(e));var t=e["[[IteratedSet]]"],r=e["[[SetNextIndex]]"],n=e["[[SetIterationKind]]"];if(t===undefined)return CreateIterResultObject(undefined,!0);if(!t._es6Set)throw new Error(Object.prototype.toString.call(t)+" does not have [[SetData]] internal slot.");for(var a=t._values,i=a.length;r<i;){var l=a[r];if(r+=1,e["[[SetNextIndex]]"]=r,l!==o)return"key+value"===n?CreateIterResultObject([l,l],!1):CreateIterResultObject(l,!1)}return e["[[IteratedSet]]"]=undefined,CreateIterResultObject(undefined,!0)}),CreateMethodProperty(i,Symbol.iterator,function s(){return this});try{CreateMethodProperty(e,"Set",n)}catch(l){e.Set=n}}(self);}if (!("from"in Array&&function(){try{return Array.from({length:-Infinity}),"a"===Array.from(new self.Set(["a"]))[0]&&"a"===Array.from(new self.Map([["a","one"]]))[0][0]}catch(r){return!1}}()
)) {!function(){function r(r){return"string"==typeof r||"object"==typeof r&&"[object String]"===t.call(r)}var t=Object.prototype.toString,e=String.prototype.match;CreateMethodProperty(Array,"from",function o(t){var o=this,a=arguments.length>1?arguments[1]:undefined;if(a===undefined)var n=!1;else{if(!1===IsCallable(a))throw new TypeError(Object.prototype.toString.call(a)+" is not a function.");var i=arguments.length>2?arguments[2]:undefined;if(i!==undefined)var l=i;else l=undefined;n=!0}var u=GetMethod(t,Symbol.iterator);if(u!==undefined){if(IsConstructor(o))var f=Construct(o);else f=ArrayCreate(0);for(var c=GetIterator(t,u),s=0;;){if(s>=Math.pow(2,53)-1){var h=new TypeError("Iteration count can not be greater than or equal 9007199254740991.");return IteratorClose(c,h)}var y=ToString(s),C=IteratorStep(c);if(!1===C)return f.length=s,f;var g=IteratorValue(C);if(n)try{var p=Call(a,l,[g,s])}catch(b){return IteratorClose(c,b)}else p=g;try{CreateDataPropertyOrThrow(f,y,p)}catch(b){return IteratorClose(c,b)}s+=1}}if(r(t))var v=e.call(t,/[\uD800-\uDBFF][\uDC00-\uDFFF]?|[^\uD800-\uDFFF]|./g)||[];else v=ToObject(t);var d=ToLength(Get(v,"length"));for(f=IsConstructor(o)?Construct(o,[d]):ArrayCreate(d),s=0;s<d;){y=ToString(s);var I=Get(v,y);p=!0===n?Call(a,l,[I,s]):I,CreateDataPropertyOrThrow(f,y,p),s+=1}return f.length=d,f})}();}if (!((function(r){"use strict"
try{var a=new r.URL("http://example.com")
if("href"in a&&"searchParams"in a){var e=new URL("http://example.com")
if(e.search="a=1&b=2","http://example.com/?a=1&b=2"===e.href&&(e.search="","http://example.com/"===e.href)){if(!("sort"in r.URLSearchParams.prototype))return!1
var t=new r.URLSearchParams("a=1"),n=new r.URLSearchParams(t)
if("a=1"!==String(n))return!1
var c=new r.URLSearchParams({a:"1"})
if("a=1"!==String(c))return!1
var h=new r.URLSearchParams([["a","1"]])
return"a=1"===String(h)}}return!1}catch(m){return!1}})(self)
)) {!function(e){"use strict";function t(t){return!!t&&("Symbol"in e&&"iterator"in e.Symbol&&"function"==typeof t[Symbol.iterator]||!!Array.isArray(t))}!function(){function n(e){var t="",n=!0;return e.forEach(function(e){var r=encodeURIComponent(e.name),a=encodeURIComponent(e.value);n||(t+="&"),t+=r+"="+a,n=!1}),t.replace(/%20/g,"+")}function r(e){return e.replace(/((%[0-9A-Fa-f]{2})*)/g,function(e,t){return decodeURIComponent(t)})}function a(e,t){var n=e.split("&");t&&-1===n[0].indexOf("=")&&(n[0]="="+n[0]);var a=[];n.forEach(function(e){if(0!==e.length){var t=e.indexOf("=");if(-1!==t)var n=e.substring(0,t),r=e.substring(t+1);else n=e,r="";n=n.replace(/\+/g," "),r=r.replace(/\+/g," "),a.push({name:n,value:r})}});var i=[];return a.forEach(function(e){i.push({name:r(e.name),value:r(e.value)})}),i}function i(e){if(c)return new s(e);var t=document.createElement("a");return t.href=e,t}function o(e){var r=this;this._list=[],e===undefined||null===e||(e instanceof o?this._list=a(String(e)):"object"==typeof e&&t(e)?Array.from(e).forEach(function(e){if(!t(e))throw TypeError();var n=Array.from(e);if(2!==n.length)throw TypeError();r._list.push({name:String(n[0]),value:String(n[1])})}):"object"==typeof e&&e?Object.keys(e).forEach(function(t){r._list.push({name:String(t),value:String(e[t])})}):(e=String(e),"?"===e.substring(0,1)&&(e=e.substring(1)),this._list=a(e))),this._url_object=null,this._setList=function(e){i||(r._list=e)};var i=!1;this._update_steps=function(){i||(i=!0,r._url_object&&("about:"===r._url_object.protocol&&-1!==r._url_object.pathname.indexOf("?")&&(r._url_object.pathname=r._url_object.pathname.split("?")[0]),r._url_object.search=n(r._list),i=!1))}}function u(e,t){var n=0;this.next=function(){if(n>=e.length)return{done:!0,value:undefined};var r=e[n++];return{done:!1,value:"key"===t?r.name:"value"===t?r.value:[r.name,r.value]}}}function l(t,n){function r(){var e=l.href.replace(/#$|\?$|\?(?=#)/g,"");l.href!==e&&(l.href=e)}function u(){m._setList(l.search?a(l.search.substring(1)):[]),m._update_steps()}if(!(this instanceof e.URL))throw new TypeError("Failed to construct 'URL': Please use the 'new' operator.");n&&(t=function(){if(c)return new s(t,n).href;var e;try{var r;if("[object OperaMini]"===Object.prototype.toString.call(window.operamini)?(e=document.createElement("iframe"),e.style.display="none",document.documentElement.appendChild(e),r=e.contentWindow.document):document.implementation&&document.implementation.createHTMLDocument?r=document.implementation.createHTMLDocument(""):document.implementation&&document.implementation.createDocument?(r=document.implementation.createDocument("http://www.w3.org/1999/xhtml","html",null),r.documentElement.appendChild(r.createElement("head")),r.documentElement.appendChild(r.createElement("body"))):window.ActiveXObject&&(r=new window.ActiveXObject("htmlfile"),r.write("<head></head><body></body>"),r.close()),!r)throw Error("base not supported");var a=r.createElement("base");a.href=n,r.getElementsByTagName("head")[0].appendChild(a);var i=r.createElement("a");return i.href=t,i.href}finally{e&&e.parentNode.removeChild(e)}}());var l=i(t||""),f=function(){if(!("defineProperties"in Object))return!1;try{var e={};return Object.defineProperties(e,{prop:{get:function(){return!0}}}),e.prop}catch(t){return!1}}(),h=f?this:document.createElement("a"),m=new o(l.search?l.search.substring(1):null);return m._url_object=h,Object.defineProperties(h,{href:{get:function(){return l.href},set:function(e){l.href=e,r(),u()},enumerable:!0,configurable:!0},origin:{get:function(){return"data:"===this.protocol.toLowerCase()?null:"origin"in l?l.origin:this.protocol+"//"+this.host},enumerable:!0,configurable:!0},protocol:{get:function(){return l.protocol},set:function(e){l.protocol=e},enumerable:!0,configurable:!0},username:{get:function(){return l.username},set:function(e){l.username=e},enumerable:!0,configurable:!0},password:{get:function(){return l.password},set:function(e){l.password=e},enumerable:!0,configurable:!0},host:{get:function(){var e={"http:":/:80$/,"https:":/:443$/,"ftp:":/:21$/}[l.protocol];return e?l.host.replace(e,""):l.host},set:function(e){l.host=e},enumerable:!0,configurable:!0},hostname:{get:function(){return l.hostname},set:function(e){l.hostname=e},enumerable:!0,configurable:!0},port:{get:function(){return l.port},set:function(e){l.port=e},enumerable:!0,configurable:!0},pathname:{get:function(){return"/"!==l.pathname.charAt(0)?"/"+l.pathname:l.pathname},set:function(e){l.pathname=e},enumerable:!0,configurable:!0},search:{get:function(){return l.search},set:function(e){l.search!==e&&(l.search=e,r(),u())},enumerable:!0,configurable:!0},searchParams:{get:function(){return m},enumerable:!0,configurable:!0},hash:{get:function(){return l.hash},set:function(e){l.hash=e,r()},enumerable:!0,configurable:!0},toString:{value:function(){return l.toString()},enumerable:!1,configurable:!0},valueOf:{value:function(){return l.valueOf()},enumerable:!1,configurable:!0}}),h}var c,s=e.URL;try{if(s){if("searchParams"in(c=new e.URL("http://example.com"))){var f=new l("http://example.com");if(f.search="a=1&b=2","http://example.com/?a=1&b=2"===f.href&&(f.search="","http://example.com/"===f.href))return}"href"in c||(c=undefined),c=undefined}}catch(m){}if(Object.defineProperties(o.prototype,{append:{value:function(e,t){this._list.push({name:e,value:t}),this._update_steps()},writable:!0,enumerable:!0,configurable:!0},"delete":{value:function(e){for(var t=0;t<this._list.length;)this._list[t].name===e?this._list.splice(t,1):++t;this._update_steps()},writable:!0,enumerable:!0,configurable:!0},get:{value:function(e){for(var t=0;t<this._list.length;++t)if(this._list[t].name===e)return this._list[t].value;return null},writable:!0,enumerable:!0,configurable:!0},getAll:{value:function(e){for(var t=[],n=0;n<this._list.length;++n)this._list[n].name===e&&t.push(this._list[n].value);return t},writable:!0,enumerable:!0,configurable:!0},has:{value:function(e){for(var t=0;t<this._list.length;++t)if(this._list[t].name===e)return!0;return!1},writable:!0,enumerable:!0,configurable:!0},set:{value:function(e,t){for(var n=!1,r=0;r<this._list.length;)this._list[r].name===e?n?this._list.splice(r,1):(this._list[r].value=t,n=!0,++r):++r;n||this._list.push({name:e,value:t}),this._update_steps()},writable:!0,enumerable:!0,configurable:!0},entries:{value:function(){return new u(this._list,"key+value")},writable:!0,enumerable:!0,configurable:!0},keys:{value:function(){return new u(this._list,"key")},writable:!0,enumerable:!0,configurable:!0},values:{value:function(){return new u(this._list,"value")},writable:!0,enumerable:!0,configurable:!0},forEach:{value:function(e){var t=arguments.length>1?arguments[1]:undefined;this._list.forEach(function(n){e.call(t,n.value,n.name)})},writable:!0,enumerable:!0,configurable:!0},toString:{value:function(){return n(this._list)},writable:!0,enumerable:!1,configurable:!0},sort:{value:function p(){for(var e=this.entries(),t=e.next(),n=[],r={};!t.done;){var a=t.value,i=a[0];n.push(i),Object.prototype.hasOwnProperty.call(r,i)||(r[i]=[]),r[i].push(a[1]),t=e.next()}n.sort();for(var o=0;o<n.length;o++)this["delete"](n[o]);for(var u=0;u<n.length;u++)i=n[u],this.append(i,r[i].shift())}}}),"Symbol"in e&&"iterator"in e.Symbol&&(Object.defineProperty(o.prototype,e.Symbol.iterator,{value:o.prototype.entries,writable:!0,enumerable:!0,configurable:!0}),Object.defineProperty(u.prototype,e.Symbol.iterator,{value:function(){return this},writable:!0,enumerable:!0,configurable:!0})),s)for(var h in s)Object.prototype.hasOwnProperty.call(s,h)&&"function"==typeof s[h]&&(l[h]=s[h]);e.URL=l,e.URLSearchParams=o}(),function(){if("1"!==new e.URLSearchParams([["a",1]]).get("a")||"1"!==new e.URLSearchParams({a:1}).get("a")){var n=e.URLSearchParams;e.URLSearchParams=function(e){if(e&&"object"==typeof e&&t(e)){var r=new n;return Array.from(e).forEach(function(e){if(!t(e))throw TypeError();var n=Array.from(e);if(2!==n.length)throw TypeError();r.append(n[0],n[1])}),r}return e&&"object"==typeof e?(r=new n,Object.keys(e).forEach(function(t){r.set(t,e[t])}),r):new n(e)}}}()}(self);}if (!("Symbol"in self&&"split"in self.Symbol
)) {Object.defineProperty(Symbol,"split",{value:Symbol("split")});}if (!("Symbol"in self&&"toPrimitive"in self.Symbol
)) {Object.defineProperty(Symbol,"toPrimitive",{value:Symbol("toPrimitive")});}if (!("Symbol"in self&&"toStringTag"in self.Symbol
)) {Object.defineProperty(Symbol,"toStringTag",{value:Symbol("toStringTag")});}if (!("Promise"in self
)) {!function(){"use strict";function n(){return tn[q][B]||D}function t(n){return n&&"object"==typeof n}function e(n){return"function"==typeof n}function r(n,t){return n instanceof t}function o(n){return r(n,A)}function i(n,t,e){if(!t(n))throw a(e)}function u(){try{return b.apply(R,arguments)}catch(n){return Y.e=n,Y}}function c(n,t){return b=n,R=t,u}function f(n,t){function e(){for(var e=0;e<o;)t(r[e],r[e+1]),r[e++]=T,r[e++]=T;o=0,r.length>n&&(r.length=n)}var r=L(n),o=0;return function(n,t){r[o++]=n,r[o++]=t,2===o&&tn.nextTick(e)}}function s(n,t){var o,i,u,f,s=0;if(!n)throw a(N);var l=n[tn[q][z]];if(e(l))i=l.call(n);else{if(!e(n.next)){if(r(n,L)){for(o=n.length;s<o;)t(n[s],s++);return s}throw a(N)}i=n}for(;!(u=i.next()).done;)if((f=c(t)(u.value,s++))===Y)throw e(i[G])&&i[G](),f.e;return s}function a(n){return new TypeError(n)}function l(n){return(n?"":Q)+(new A).stack}function h(n,t){var e="on"+n.toLowerCase(),r=F[e];E&&E.listeners(n).length?n===X?E.emit(n,t._v,t):E.emit(n,t):r?r({reason:t._v,promise:t}):tn[n](t._v,t)}function v(n){return n&&n._s}function _(n){if(v(n))return new n(Z);var t,r,o;return t=new n(function(n,e){if(t)throw a();r=n,o=e}),i(r,e),i(o,e),t}function d(n,t){var e=!1;return function(r){e||(e=!0,I&&(n[M]=l(!0)),t===U?g(n,r):y(n,t,r))}}function p(n,t,r,o){return e(r)&&(t._onFulfilled=r),e(o)&&(n[J]&&h(W,n),t._onRejected=o),I&&(t._p=n),n[n._c++]=t,n._s!==$&&rn(n,t),t}function m(n){if(n._umark)return!0;n._umark=!0;for(var t,e=0,r=n._c;e<r;)if(t=n[e++],t._onRejected||m(t))return!0}function w(n,t){function e(n){return r.push(n.replace(/^\s+|\s+$/g,""))}var r=[];return I&&(t[M]&&e(t[M]),function o(n){n&&K in n&&(o(n._next),e(n[K]+""),o(n._p))}(t)),(n&&n.stack?n.stack:n)+("\n"+r.join("\n")).replace(nn,"")}function j(n,t){return n(t)}function y(n,t,e){var r=0,i=n._c;if(n._s===$)for(n._s=t,n._v=e,t===O&&(I&&o(e)&&(e.longStack=w(e,n)),on(n));r<i;)rn(n,n[r++]);return n}function g(n,r){if(r===n&&r)return y(n,O,a(V)),n;if(r!==S&&(e(r)||t(r))){var o=c(k)(r);if(o===Y)return y(n,O,o.e),n;e(o)?(I&&v(r)&&(n._next=r),v(r)?x(n,r,o):tn.nextTick(function(){x(n,r,o)})):y(n,U,r)}else y(n,U,r);return n}function k(n){return n.then}function x(n,t,e){var r=c(e,t)(function(e){t&&(t=S,g(n,e))},function(e){t&&(t=S,y(n,O,e))});r===Y&&t&&(y(n,O,r.e),t=S)}var T,b,R,S=null,C="object"==typeof self,F=self,P=F.Promise,E=F.process,H=F.console,I=!0,L=Array,A=Error,O=1,U=2,$=3,q="Symbol",z="iterator",B="species",D=q+"("+B+")",G="return",J="_uh",K="_pt",M="_st",N="Invalid argument",Q="\nFrom previous ",V="Chaining cycle detected for promise",W="rejectionHandled",X="unhandledRejection",Y={e:S},Z=function(){},nn=/^.+\/node_modules\/yaku\/.+\n?/gm,tn=function(n){var r,o=this;if(!t(o)||o._s!==T)throw a("Invalid this");if(o._s=$,I&&(o[K]=l()),n!==Z){if(!e(n))throw a(N);r=c(n)(d(o,U),d(o,O)),r===Y&&y(o,O,r.e)}};tn["default"]=tn,function en(n,t){for(var e in t)n[e]=t[e]}(tn.prototype,{then:function(n,t){if(this._s===undefined)throw a();return p(this,_(tn.speciesConstructor(this,tn)),n,t)},"catch":function(n){return this.then(T,n)},"finally":function(n){return this.then(function(t){return tn.resolve(n()).then(function(){return t})},function(t){return tn.resolve(n()).then(function(){throw t})})},_c:0,_p:S}),tn.resolve=function(n){return v(n)?n:g(_(this),n)},tn.reject=function(n){return y(_(this),O,n)},tn.race=function(n){var t=this,e=_(t),r=function(n){y(e,U,n)},o=function(n){y(e,O,n)},i=c(s)(n,function(n){t.resolve(n).then(r,o)});return i===Y?t.reject(i.e):e},tn.all=function(n){function t(n){y(o,O,n)}var e,r=this,o=_(r),i=[];return(e=c(s)(n,function(n,u){r.resolve(n).then(function(n){i[u]=n,--e||y(o,U,i)},t)}))===Y?r.reject(e.e):(e||y(o,U,[]),o)},tn.Symbol=F[q]||{},c(function(){Object.defineProperty(tn,n(),{get:function(){return this}})})(),tn.speciesConstructor=function(t,e){var r=t.constructor;return r?r[n()]||e:e},tn.unhandledRejection=function(n,t){H&&H.error("Uncaught (in promise)",I?t.longStack:w(n,t))},tn.rejectionHandled=Z,tn.enableLongStackTrace=function(){I=!0},tn.nextTick=C?function(n){P?new P(function(n){n()}).then(n):setTimeout(n)}:E.nextTick,tn._s=1;var rn=f(999,function(n,t){var e,r;return(r=n._s!==O?t._onFulfilled:t._onRejected)===T?void y(t,n._s,n._v):(e=c(j)(r,n._v))===Y?void y(t,O,e.e):void g(t,e)}),on=f(9,function(n){m(n)||(n[J]=1,h(X,n))});F.Promise=tn}();}if (!("Promise"in self&&"allSettled"in self.Promise
)) {!function(){CreateMethodProperty(Promise,"allSettled",function e(r){var t=this;if("object"!==Type(t))throw new TypeError("`this` value must be an object");var n;if(Array.isArray(r))n=r;else try{n=IterableToList(r)}catch(o){return Promise.reject(new TypeError("Argument of Promise.allSettled is not iterable"))}var a=n.map(function(e){var r=function(e){return{status:"fulfilled",value:e}},n=function(e){return{status:"rejected",reason:e}},a=t.resolve(e);try{return a.then(r,n)}catch(o){return t.reject(o)}});return t.all(a)})}();}if (!("Promise"in self&&"any"in self.Promise
)) {!function(){var r=function(r){return r};CreateMethodProperty(Promise,"any",function e(t){var n=this;if("object"!==Type(n))throw new TypeError("`this` value must be an object");var o;if(Array.isArray(t))o=t;else try{o=IterableToList(t)}catch(c){return Promise.reject(new TypeError("Argument of Promise.any is not iterable"))}var a=function(r){return n.reject(r)},i=o.map(function(e){var t=n.resolve(e);try{return t.then(a,r)}catch(o){return o}});return n.all(i).then(function(r){throw new AggregateError(r,"Every promise rejected")},r)})}();}if (!("Promise"in self&&"finally"in self.Promise.prototype
)) {!function(){var t=Function.prototype.bind.call(Function.prototype.call,Promise.prototype.then),o=function(t,o){return new t(function(t){t(o())})};CreateMethodProperty(Promise.prototype,"finally",function(e){var r=this;if("object"!==Type(r))throw new TypeError("Method %PromisePrototype%.finally called on incompatible receiver "+Object.prototype.toString.call(r));var n=SpeciesConstructor(r,Promise);if(!1===IsCallable(e))var i=e,c=e;else i=function(r){return t(o(n,e),function(){return r})},c=function(r){return t(o(n,e),function(){throw r})};return t(r,i,c)})}();}if (!("Symbol"in self&&"unscopables"in self.Symbol
)) {Object.defineProperty(Symbol,"unscopables",{value:Symbol("unscopables")});}if (!((function(){try{if("WeakMap"in self&&0===self.WeakMap.length){var e={},t=new self.WeakMap([[e,"test"]])
return"test"===t.get(e)&&!1===t["delete"](0)}return!1}catch(a){return!1}})()
)) {!function(e){var t=Symbol("undef"),r=function a(){if(!(this instanceof a))throw new TypeError('Constructor WeakMap requires "new"');var e=OrdinaryCreateFromConstructor(this,a.prototype,{_keys:[],_values:[],_es6WeakMap:!0}),t=arguments.length>0?arguments[0]:undefined;if(null===t||t===undefined)return e;var r=Get(e,"set");if(!IsCallable(r))throw new TypeError("WeakMap.prototype.set is not a function");try{for(var o=GetIterator(t);;){var p=IteratorStep(o);if(!1===p)return e;var n=IteratorValue(p);if("object"!==Type(n))try{throw new TypeError("Iterator value "+n+" is not an entry object")}catch(s){return IteratorClose(o,s)}try{var i=Get(n,"0"),l=Get(n,"1");Call(r,e,[i,l])}catch(u){return IteratorClose(o,u)}}}catch(u){if(Array.isArray(t)||"[object Arguments]"===Object.prototype.toString.call(t)||t.callee){var y,c=t.length;for(y=0;y<c;y++)i=t[y][0],l=t[y][1],Call(r,e,[i,l])}}return e};Object.defineProperty(r,"prototype",{configurable:!1,enumerable:!1,writable:!1,value:{}}),CreateMethodProperty(r.prototype,"constructor",r),CreateMethodProperty(r.prototype,"delete",function(e){var r=this;if("object"!==Type(r))throw new TypeError("Method WeakMap.prototype.clear called on incompatible receiver "+Object.prototype.toString.call(r));if(!0!==r._es6WeakMap)throw new TypeError("Method WeakMap.prototype.clear called on incompatible receiver "+Object.prototype.toString.call(r));var o=r._keys;if("object"!==Type(e))return!1;for(var a=0;a<o.length;a++)if(r._keys[a]!==t&&SameValue(r._keys[a],e))return this._keys[a]=t,this._values[a]=t,this._size=--this._size,!0;return!1}),CreateMethodProperty(r.prototype,"get",function p(e){var r=this;if("object"!==Type(r))throw new TypeError("Method WeakMap.prototype.get called on incompatible receiver "+Object.prototype.toString.call(r));if(!0!==r._es6WeakMap)throw new TypeError("Method WeakMap.prototype.get called on incompatible receiver "+Object.prototype.toString.call(r));var o=r._keys;if("object"!==Type(e))return undefined;for(var a=0;a<o.length;a++)if(r._keys[a]!==t&&SameValue(r._keys[a],e))return r._values[a];return undefined}),CreateMethodProperty(r.prototype,"has",function n(e){var r=this;if("object"!=typeof r)throw new TypeError("Method WeakMap.prototype.has called on incompatible receiver "+Object.prototype.toString.call(r));if(!0!==r._es6WeakMap)throw new TypeError("Method WeakMap.prototype.has called on incompatible receiver "+Object.prototype.toString.call(r));var o=r._keys;if("object"!==Type(e))return!1;for(var a=0;a<o.length;a++)if(r._keys[a]!==t&&SameValue(r._keys[a],e))return!0;return!1}),CreateMethodProperty(r.prototype,"set",function i(e,r){var o=this;if("object"!==Type(o))throw new TypeError("Method WeakMap.prototype.set called on incompatible receiver "+Object.prototype.toString.call(o));if(!0!==o._es6WeakMap)throw new TypeError("Method WeakMap.prototype.set called on incompatible receiver "+Object.prototype.toString.call(o));var a=o._keys;if("object"!==Type(e))throw new TypeError("Invalid value used as weak map key");for(var p=0;p<a.length;p++)if(o._keys[p]!==t&&SameValue(o._keys[p],e))return o._values[p]=r,o;var n={"[[Key]]":e,"[[Value]]":r};return o._keys.push(n["[[Key]]"]),o._values.push(n["[[Value]]"]),o}),Object.defineProperty(r.prototype,Symbol.toStringTag,{configurable:!0,enumerable:!1,writable:!1,value:"WeakMap"}),"name"in r||Object.defineProperty(r,"name",{configurable:!0,enumerable:!1,writable:!1,value:"WeakMap"});try{CreateMethodProperty(e,"WeakMap",r)}catch(o){e.WeakMap=r}}(self);}if (!((function(e){try{if(Object.prototype.hasOwnProperty.call(e,"WeakSet")&&0===e.WeakSet.length){var t={},r=new e.WeakSet([t])
return r.has(t)&&!1===r["delete"](0)}return!1}catch(a){return!1}})(self)
)) {!function(e){var t=Symbol("undef"),r=function a(){if(!(this instanceof a))throw new TypeError('Constructor WeakSet requires "new"');var e=OrdinaryCreateFromConstructor(this,a.prototype,{_values:[],_size:0,_es6WeakSet:!0}),t=arguments.length>0?arguments[0]:undefined;if(null===t||t===undefined)return e;var r=Get(e,"add");if(!IsCallable(r))throw new TypeError("WeakSet.prototype.add is not a function");try{for(var o=GetIterator(t);;){var n=IteratorStep(o);if(!1===n)return e;var l=IteratorValue(n);try{Call(r,e,[l])}catch(c){return IteratorClose(o,c)}}}catch(c){if(IsArray(t)||"[object Arguments]"===Object.prototype.toString.call(t)||t.callee){var p,i=t.length;for(p=0;p<i;p++)Call(r,e,[t[p]])}}return e};Object.defineProperty(r,"prototype",{configurable:!1,enumerable:!1,writable:!1,value:{}}),CreateMethodProperty(r.prototype,"add",function n(e){var r=this;if("object"!==Type(r))throw new TypeError("Method WeakSet.prototype.add called on incompatible receiver "+Object.prototype.toString.call(r));if(!0!==r._es6WeakSet)throw new TypeError("Method WeakSet.prototype.add called on incompatible receiver "+Object.prototype.toString.call(r));if("object"!==Type(e))throw new TypeError("Invalid value used in weak set");for(var o=r._values,a=0;a<o.length;a++){var n=o[a];if(n!==t&&SameValueZero(n,e))return r}return r._values.push(e),r}),CreateMethodProperty(r.prototype,"constructor",r),CreateMethodProperty(r.prototype,"delete",function(e){var r=this;if("object"!==Type(r))throw new TypeError("Method WeakSet.prototype.delete called on incompatible receiver "+Object.prototype.toString.call(r));if(!0!==r._es6WeakSet)throw new TypeError("Method WeakSet.prototype.delete called on incompatible receiver "+Object.prototype.toString.call(r));if("object"!==Type(e))return!1;for(var o=r._values,a=0;a<o.length;a++){var n=o[a];if(n!==t&&SameValueZero(n,e))return o[a]=t,!0}return!1}),CreateMethodProperty(r.prototype,"has",function l(e){var r=this;if("object"!==Type(r))throw new TypeError("Method WeakSet.prototype.has called on incompatible receiver "+Object.prototype.toString.call(r));if(!0!==r._es6WeakSet)throw new TypeError("Method WeakSet.prototype.has called on incompatible receiver "+Object.prototype.toString.call(r));var o=r._values;if("object"!==Type(e))return!1;for(var a=0;a<o.length;a++){var n=o[a];if(n!==t&&SameValueZero(n,e))return!0}return!1}),"name"in r||Object.defineProperty(r,"name",{configurable:!0,enumerable:!1,writable:!1,value:"WeakSet"});try{CreateMethodProperty(e,"WeakSet",r)}catch(o){e.WeakSet=r}}(self);}function TrimString(e,u){var r=RequireObjectCoercible(e),t=ToString(r),n=/[\x09\x0A\x0B\x0C\x0D\x20\xA0\u1680\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029\uFEFF]+/.source;if("start"===u)var p=String.prototype.replace.call(t,new RegExp("^"+n,"g"),"");else p="end"===u?String.prototype.replace.call(t,new RegExp(n+"$","g"),""):String.prototype.replace.call(t,new RegExp("^"+n+"|"+n+"$","g"),"");return p}if (!("trim"in String.prototype&&function(){var r="​᠎"
return!"\t\n\x0B\f\r                　\u2028\u2029\ufeff".trim()&&r.trim()===r}()
)) {CreateMethodProperty(String.prototype,"trim",function t(){"use strict";var t=this;return TrimString(t,"start+end")});}if (!("parseFloat"in Number&&1/parseFloat("\t\n\x0B\f\r                　\u2028\u2029\ufeff-0")==-Infinity
)) {!function(r,t){var a=function o(t){var a=String(t).trim(),e=r(a);return 0===e&&"-"==a.charAt(0)?-0:e};try{CreateMethodProperty(t,"parseFloat",a)}catch(e){t.parseFloat=a}CreateMethodProperty(Number,"parseFloat",t.parseFloat)}(parseFloat,this);}if (!("parseInt"in Number&&8===Number.parseInt("08")
)) {!function(t,r){var e=function a(r,e){var n=String(r).trim();return t(n,e>>>0||(/^[-+]?0[xX]/.test(n)?16:10))};try{CreateMethodProperty(r,"parseInt",e)}catch(n){r.parseInt=e}CreateMethodProperty(Number,"parseInt",r.parseInt)}(parseInt,this);}function UTF16Decode(e,n){return 1024*(e-55296)+(n-56320)+65536}if (!("codePointAt"in String.prototype
)) {CreateMethodProperty(String.prototype,"codePointAt",function e(t){var r=RequireObjectCoercible(this),o=ToString(r),n=ToInteger(t),i=o.length;if(n<0||n>=i)return undefined;var c=String.prototype.charCodeAt.call(o,n);if(c<55296||c>56319||n+1===i)return c;var a=String.prototype.charCodeAt.call(o,n+1);return a<56320||a>57343?c:UTF16Decode(c,a)});}function AdvanceStringIndex(e,n,t){if(n>Number.MAX_SAFE_INTEGER)throw new TypeError("Assertion failed: `index` must be <= 2**53");return!1===t?n+1:n+1>=e.length?n+1:n+e.codePointAt(n).length}function CreateRegExpStringIterator(e,t,r,n){var a={};return CreateMethodProperty(a,"next",function i(){if(!0===this["[[Done]]"])return CreateIterResultObject(undefined,!0);var a=RegExpExec(e,t);if(null===a)return this["[[Done]]"]=!0,CreateIterResultObject(undefined,!0);if(!1===r){var i=CreateIterResultObject(a,!1);return this["[[Done]]"]=!0,i}if(""===ToString(Get(a,"0"))){var o=ToLength(Get(e,"lastIndex")),u=AdvanceStringIndex(t,o,n);e.lastIndex=u}return CreateIterResultObject(a,!1)}),Object.defineProperty(a,Symbol.toStringTag,{configurable:!0,enumerable:!1,writable:!1,value:"RegExp String Iterator"}),CreateMethodProperty(a,Symbol.iterator,function o(){return this}),a}if (!("Symbol"in self&&"matchAll"in self.Symbol&&!!RegExp.prototype[self.Symbol.matchAll]
)) {var supportsRegexpLiteralConstructorWithFlags=function(){try{return new RegExp(/x/,"g"),!0}catch(t){return!1}}();CreateMethodProperty(RegExp.prototype,Symbol.matchAll,function(t){"use strict";var e=this;if("object"!==Type(e))throw new TypeError("Method called on incompatible type: must be an object.");var r=ToString(t),o=SpeciesConstructor(e,RegExp),n=ToString(Get(e,"flags"));"flags"in RegExp.prototype||(n="",!0===e.global&&(n+="g"),!0===e.ignoreCase&&(n+="i"),!0===e.multiline&&(n+="m"));var a=Construct(o,[supportsRegexpLiteralConstructorWithFlags?e:e.source,n]),i=ToLength(Get(e,"lastIndex"));a.lastIndex=i;var p=n.indexOf("g")>-1,s=n.indexOf("u")>-1;return CreateRegExpStringIterator(a,r,p,s)});}if (!("matchAll"in String.prototype
)) {CreateMethodProperty(String.prototype,"matchAll",function e(l){"use strict";var r=RequireObjectCoercible(this);if(l!==undefined&&null!==l){if(IsRegExp(l)){var t=Get(l,"flags");if(!("flags"in RegExp.prototype||!0===l.global))throw TypeError("");if("flags"in RegExp.prototype&&(RequireObjectCoercible(t),-1===ToString(t).indexOf("g")))throw TypeError("")}var o="Symbol"in self&&"matchAll"in self.Symbol?GetMethod(l,self.Symbol.matchAll):undefined;if(o!==undefined)return Call(o,l,[r])}var i=ToString(r),n=new RegExp(l,"g");return Invoke(n,"Symbol"in self&&"matchAll"in self.Symbol&&self.Symbol.matchAll,[i])});}function UTF16Encoding(n){return n<=65535?n:[Math.floor((n-65536)/1024)+55296,(n-65536)%1024+56320]}if (!("fromCodePoint"in String&&1===String.fromCodePoint.length
)) {CreateMethodProperty(String,"fromCodePoint",function r(o){for(var t=[],e=arguments,n=arguments.length,a=[],i=0;i<n;){a=[];var l=e[i],p=ToNumber(l);if(!1===SameValue(p,ToInteger(p)))throw new RangeError("Invalid code point "+Object.prototype.toString.call(p));if(p<0||p>1114111)throw new RangeError("Invalid code point "+Object.prototype.toString.call(p));var c=UTF16Encoding(p);IsArray(c)?a=a.concat(c):a.push(c),i+=1,t.push(String.fromCharCode.apply(null,a))}return 0===n?"":t.join("")});}var Iterator=function(){var e=function(){return this.length=0,this},t=function(e){if("function"!=typeof e)throw new TypeError(e+" is not a function");return e},_=function(e,n){if(!(this instanceof _))return new _(e,n);Object.defineProperties(this,{__list__:{writable:!0,value:e},__context__:{writable:!0,value:n},__nextIndex__:{writable:!0,value:0}}),n&&(t(n.on),n.on("_add",this._onAdd.bind(this)),n.on("_delete",this._onDelete.bind(this)),n.on("_clear",this._onClear.bind(this)))};return Object.defineProperties(_.prototype,Object.assign({constructor:{value:_,configurable:!0,enumerable:!1,writable:!0},_next:{value:function(){var e;if(this.__list__)return this.__redo__&&(e=this.__redo__.shift())!==undefined?e:this.__nextIndex__<this.__list__.length?this.__nextIndex__++:void this._unBind()},configurable:!0,enumerable:!1,writable:!0},next:{value:function(){return this._createResult(this._next())},configurable:!0,enumerable:!1,writable:!0},_createResult:{value:function(e){return e===undefined?{done:!0,value:undefined}:{done:!1,value:this._resolve(e)}},configurable:!0,enumerable:!1,writable:!0},_resolve:{value:function(e){return this.__list__[e]},configurable:!0,enumerable:!1,writable:!0},_unBind:{value:function(){this.__list__=null,delete this.__redo__,this.__context__&&(this.__context__.off("_add",this._onAdd.bind(this)),this.__context__.off("_delete",this._onDelete.bind(this)),this.__context__.off("_clear",this._onClear.bind(this)),this.__context__=null)},configurable:!0,enumerable:!1,writable:!0},toString:{value:function(){return"[object Iterator]"},configurable:!0,enumerable:!1,writable:!0}},{_onAdd:{value:function(e){if(!(e>=this.__nextIndex__)){if(++this.__nextIndex__,!this.__redo__)return void Object.defineProperty(this,"__redo__",{value:[e],configurable:!0,enumerable:!1,writable:!1});this.__redo__.forEach(function(t,_){t>=e&&(this.__redo__[_]=++t)},this),this.__redo__.push(e)}},configurable:!0,enumerable:!1,writable:!0},_onDelete:{value:function(e){var t;e>=this.__nextIndex__||(--this.__nextIndex__,this.__redo__&&(t=this.__redo__.indexOf(e),-1!==t&&this.__redo__.splice(t,1),this.__redo__.forEach(function(t,_){t>e&&(this.__redo__[_]=--t)},this)))},configurable:!0,enumerable:!1,writable:!0},_onClear:{value:function(){this.__redo__&&e.call(this.__redo__),this.__nextIndex__=0},configurable:!0,enumerable:!1,writable:!0}})),Object.defineProperty(_.prototype,Symbol.iterator,{value:function(){return this},configurable:!0,enumerable:!1,writable:!0}),Object.defineProperty(_.prototype,Symbol.toStringTag,{value:"Iterator",configurable:!1,enumerable:!1,writable:!0}),_}();var ArrayIterator=function(){var e=function(t,r){if(!(this instanceof e))return new e(t,r);Iterator.call(this,t),r=r?String.prototype.includes.call(r,"key+value")?"key+value":String.prototype.includes.call(r,"key")?"key":"value":"value",Object.defineProperty(this,"__kind__",{value:r,configurable:!1,enumerable:!1,writable:!1})};return Object.setPrototypeOf&&Object.setPrototypeOf(e,Iterator.prototype),e.prototype=Object.create(Iterator.prototype,{constructor:{value:e,configurable:!0,enumerable:!1,writable:!0},_resolve:{value:function(e){return"value"===this.__kind__?this.__list__[e]:"key+value"===this.__kind__?[e,this.__list__[e]]:e},configurable:!0,enumerable:!1,writable:!0},toString:{value:function(){return"[object Array Iterator]"},configurable:!0,enumerable:!1,writable:!0}}),e}();if (!("Symbol"in self&&"iterator"in self.Symbol&&!!Array.prototype.entries
)) {CreateMethodProperty(Array.prototype,"entries",function r(){var r=ToObject(this);return new ArrayIterator(r,"key+value")});}if (!("Symbol"in self&&"iterator"in self.Symbol&&!!Array.prototype.keys
)) {CreateMethodProperty(Array.prototype,"keys",function r(){var r=ToObject(this);return new ArrayIterator(r,"key")});}if (!("values"in Array.prototype
)) {"Symbol"in self&&"iterator"in Symbol&&"function"==typeof Array.prototype[Symbol.iterator]?CreateMethodProperty(Array.prototype,"values",Array.prototype[Symbol.iterator]):CreateMethodProperty(Array.prototype,"values",function r(){var r=ToObject(this);return new ArrayIterator(r,"value")});}if (!("Symbol"in self&&"iterator"in self.Symbol&&!!Array.prototype[self.Symbol.iterator]
)) {CreateMethodProperty(Array.prototype,Symbol.iterator,Array.prototype.values);}var StringIterator=function(){var t=function(e){if(!(this instanceof t))return new t(e);e=String(e),Iterator.call(this,e),Object.defineProperty(this,"__length__",{value:e.length,configurable:!1,enumerable:!1,writable:!1})};return Object.setPrototypeOf&&Object.setPrototypeOf(t,Iterator),t.prototype=Object.create(Iterator.prototype,{constructor:{value:t,configurable:!0,enumerable:!1,writable:!0},_next:{value:function(){if(this.__list__)return this.__nextIndex__<this.__length__?this.__nextIndex__++:void this._unBind()},configurable:!0,enumerable:!1,writable:!0},_resolve:{value:function(t){var e,r=this.__list__[t];return this.__nextIndex__===this.__length__?r:(e=r.charCodeAt(0),e>=55296&&e<=56319?r+this.__list__[this.__nextIndex__++]:r)},configurable:!0,enumerable:!1,writable:!0},toString:{value:function(){return"[object String Iterator]"},configurable:!0,enumerable:!1,writable:!0}}),t}();if (!("Symbol"in self&&"iterator"in self.Symbol&&!!String.prototype[self.Symbol.iterator]
)) {CreateMethodProperty(String.prototype,Symbol.iterator,function(){var r=RequireObjectCoercible(this),t=ToString(r);return new StringIterator(t)});}if (!("atob"in self
)) {!function(e){"use strict";if("object"==typeof exports&&null!=exports&&"number"!=typeof exports.nodeType)module.exports=e();else if("function"==typeof define&&null!=define.amd)define([],e);else{var t=e(),o="undefined"!=typeof self?self:$.global;"function"!=typeof o.btoa&&(o.btoa=t.btoa),"function"!=typeof o.atob&&(o.atob=t.atob)}}(function(){"use strict";function e(e){this.message=e}function t(t){for(var o,n,a=String(t),i=0,f=r,c="";a.charAt(0|i)||(f="=",i%1);c+=f.charAt(63&o>>8-i%1*8)){if((n=a.charCodeAt(i+=.75))>255)throw new e("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");o=o<<8|n}return c}function o(t){var o=String(t).replace(/[=]+$/,"");if(o.length%4==1)throw new e("'atob' failed: The string to be decoded is not correctly encoded.");for(var n,a,i=0,f=0,c="";a=o.charAt(f++);~a&&(n=i%4?64*n+a:a,i++%4)?c+=String.fromCharCode(255&n>>(-2*i&6)):0)a=r.indexOf(a);return c}var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return e.prototype=new Error,e.prototype.name="InvalidCharacterError",{btoa:t,atob:o}});}if (!("Blob"in self&&function(){try{return new Blob,!0}catch(n){return!1}}()&&function(){try{return Blob(),!1}catch(n){return!0}}()
)) {!function(t){"use strict";t.URL=t.URL||t.webkitURL;var e,n=t.ArrayBuffer,o=t.Uint8Array,r=function(t){return Object.prototype.toString.call(t).match(/^\[object\s(.*)\]$/)[1]},a=t.BlobBuilder||t.WebKitBlobBuilder||t.MozBlobBuilder||function(t){var a=function y(){this.data=[]};e=function B(t,e,n){this.data=t,this.size=t.length,this.type=e,this.encoding=n};var i=a.prototype,c=e.prototype,l=t.FileReaderSync,s=function(t){this.code=this[this.name=t]},p="NOT_FOUND_ERR SECURITY_ERR ABORT_ERR NOT_READABLE_ERR ENCODING_ERR NO_MODIFICATION_ALLOWED_ERR INVALID_STATE_ERR SYNTAX_ERR".split(" "),d=p.length,u=t.URL||t.webkitURL||t,f=u.createObjectURL,b=u.revokeObjectURL,h=u,R=t.btoa,g=t.atob,w=/^[\w-]+:\/*\[?[\w.:-]+\]?(?::[0-9]+)?/;for(e.fake=c.fake=!0;d--;)s.prototype[p[d]]=d+1;return t.URL||(h=t.URL=function(t){var e,n=document.createElementNS("http://www.w3.org/1999/xhtml","a");return n.href=t,"origin"in n||("data:"===n.protocol.toLowerCase()?n.origin=null:(e=t.match(w),n.origin=e&&e[1])),n}),h.createObjectURL=function(t){var n,o=t.type;return null===o&&(o="application/octet-stream"),t instanceof e?(n="data:"+o,"base64"===t.encoding?n+";base64,"+t.data:"URI"===t.encoding?n+","+decodeURIComponent(t.data):R?n+";base64,"+R(t.data):n+","+encodeURIComponent(t.data)):f?f.call(u,t):void 0},h.revokeObjectURL=function(t){"data:"!==t.substring(0,5)&&b&&b.call(u,t)},i.append=function(t){var a=this.data;if(o&&(t instanceof n||t instanceof o)){for(var i="",c=new o(t),p=0,d=c.length;p<d;p++)i+=String.fromCharCode(c[p]);a.push(i)}else if("Blob"===r(t)||"File"===r(t)){if(!l)throw new s("NOT_READABLE_ERR");var u=new l;a.push(u.readAsBinaryString(t))}else t instanceof e?"base64"===t.encoding&&g?a.push(g(t.data)):"URI"===t.encoding?a.push(decodeURIComponent(t.data)):"raw"===t.encoding&&a.push(t.data):("string"!=typeof t&&(t+=""),a.push(unescape(encodeURIComponent(t))))},i.getBlob=function(t){return arguments.length||(t=null),new e(this.data.join(""),t,"raw")},i.toString=function(){return"[object BlobBuilder]"},c.slice=function(){var t=arguments[0],n=arguments[1],o=arguments[2],r=arguments.length;return r<3&&(o=""),new e(this.data.slice(t,r>1?n:this.data.length),o,this.encoding)},c.toString=function(){return"[object Blob]"},c.close=function(){this.size=0,delete this.data},a}(t);!function(){try{(new a).append(1)}catch(i){var t=a.prototype.append;a.prototype.append=function i(a){o&&(a instanceof n||a instanceof o)||"Blob"===r(a)||"File"===r(a)||e&&a instanceof e||"string"!=typeof a&&(a+=""),1 in arguments?t.call(this,a,arguments[1]):t.call(this,a)}}}(),t.Blob=function(){var t=arguments[0],e=arguments[1];try{var n=this instanceof Blob}catch(p){}if(!1===n)throw new TypeError("Failed to construct 'Blob': Please use the 'new' operator, this DOM object constructor cannot be called as a function.");if(t!==undefined&&"number"!=typeof t.length)throw new TypeError("Failed to construct 'Blob': Iterator getter is not callable.");if(void 0!==t&&"object"!=typeof t)throw new TypeError("Failed to construct 'Blob': Iterator getter is not callable.");if(void 0!==e&&"object"!=typeof e)throw new TypeError("Failed to construct 'Blob': parameter 2 ('options') is not an object.");var r=e?e.type||"":"",i=new a;if(t)for(var c=0,l=t.length;c<l;c++)void 0!==o&&t[c]instanceof o?i.append(t[c].buffer):i.append(t[c]);var s=i.getBlob(r);return!s.slice&&s.webkitSlice&&(s.slice=s.webkitSlice),s};var i=Object.getPrototypeOf||function(t){return t.__proto__};t.Blob.prototype=i(new t.Blob)}(self);}if (!("toBlob"in HTMLCanvasElement.prototype
)) {CreateMethodProperty(HTMLCanvasElement.prototype,"toBlob",function(t,e,o){var a=this.toDataURL(e,o).split(",")[1];setTimeout(function(){for(var o=atob(a),n=o.length,r=new Uint8Array(n),i=0;i<n;i++)r[i]=o.charCodeAt(i);t(new Blob([r],{type:e||"image/png"}))})});}if (!("document"in self&&"Document"in self
)) {"undefined"==typeof WorkerGlobalScope&&"function"!=typeof importScripts&&(self.HTMLDocument?self.Document=self.HTMLDocument:(self.Document=self.HTMLDocument=document.constructor=new Function("return function Document() {}")(),self.Document.prototype=document));}if (!("Element"in self&&"HTMLElement"in self
)) {!function(){function e(){return l--||clearTimeout(t),!(!document.body||document.body.prototype||!/(complete|interactive)/.test(document.readyState))&&(m(document,!0),t&&document.body.prototype&&clearTimeout(t),!!document.body.prototype)}if(!("Element"in self&&"HTMLElement"in self)){if(window.Element&&!window.HTMLElement)return void(window.HTMLElement=window.Element);window.Element=window.HTMLElement=new Function("return function Element() {}")();var t,n=document.appendChild(document.createElement("body")),o=n.appendChild(document.createElement("iframe")),r=o.contentWindow.document,c=Element.prototype=r.appendChild(r.createElement("*")),d={},m=function(e,t){var n,o,r,c=e.childNodes||[],u=-1;if(1===e.nodeType&&e.constructor!==Element){e.constructor=Element;for(n in d)o=d[n],e[n]=o}for(;r=t&&c[++u];)m(r,t);return e},u=document.getElementsByTagName("*"),i=document.createElement,l=100;c.attachEvent("onpropertychange",function(e){for(var t,n=e.propertyName,o=!Object.prototype.hasOwnProperty.call(d,n),r=c[n],m=d[n],i=-1;t=u[++i];)1===t.nodeType&&(o||t[n]===m)&&(t[n]=r);d[n]=r}),c.constructor=Element,c.hasAttribute||(c.hasAttribute=function a(e){return null!==this.getAttribute(e)}),e()||(document.onreadystatechange=e,t=setInterval(e,25)),document.createElement=function p(e){var t=i(String(e).toLowerCase());return m(t)},document.removeChild(n)}}();}if (!("document"in self&&"classList"in document.documentElement&&"Element"in self&&"classList"in Element.prototype&&function(){var e=document.createElement("span")
return e.classList.add("a","b"),e.classList.contains("b")}()
)) {!function(e){var t=!0,r=function(e,r,n,i){Object.defineProperty?Object.defineProperty(e,r,{configurable:!1===t||!!i,get:n}):e.__defineGetter__(r,n)};try{r({},"support")}catch(i){t=!1}var n=function(e,i,l){r(e.prototype,i,function(){var e,c=this,s="__defineGetter__DEFINE_PROPERTY"+i;if(c[s])return e;if(c[s]=!0,!1===t){for(var o,a=n.mirror||document.createElement("div"),f=a.childNodes,d=f.length,m=0;m<d;++m)if(f[m]._R===c){o=f[m];break}o||(o=a.appendChild(document.createElement("div"))),e=DOMTokenList.call(o,c,l)}else e=new _DOMTokenList(c,l);return r(c,i,function(){return e}),delete c[s],e},!0)};n(e.Element,"classList","className"),n(e.HTMLElement,"classList","className"),n(e.HTMLLinkElement,"relList","rel"),n(e.HTMLAnchorElement,"relList","rel"),n(e.HTMLAreaElement,"relList","rel")}(self);}if (!("document"in self&&"cloneNode"in document.documentElement&&function(){var e=document.createElement("div"),n=document.createElement("input")
n.type="radio",n.checked=!0,e.appendChild(n)
var c,t=n.cloneNode(!1)
try{c=e.cloneNode()}catch(d){return!1}return t.checked&&void 0!==c&&0===c.childNodes.length}()
)) {Element.prototype.cloneNode=function(e,t){return function(t){void 0===t&&(t=!1);var c=e.call(this,t);return"checked"in this&&(c.checked=this.checked),c}}(Element.prototype.cloneNode);}if (!("document"in self&&"nextElementSibling"in document.documentElement
)) {Object.defineProperty(Element.prototype,"nextElementSibling",{get:function(){for(var e=this.nextSibling;e&&1!==e.nodeType;)e=e.nextSibling;return e}});}if (!("document"in self&&"previousElementSibling"in document.documentElement
)) {Object.defineProperty(Element.prototype,"previousElementSibling",{get:function(){for(var e=this.previousSibling;e&&1!==e.nodeType;)e=e.previousSibling;return e}});}if (!("document"in self&&"toggleAttribute"in document.documentElement
)) {Element.prototype.toggleAttribute=function t(e,i){return i!==undefined&&(i=!!i),null!==this.getAttribute(e)?!!i||(this.removeAttribute(e),!1):!1!==i&&(this.setAttribute(e,""),!0)};}if (!(document.contains
)) {!function(){function e(e){if(!(0 in arguments))throw new TypeError("1 argument is required");do{if(this===e)return!0}while(e=e&&e.parentNode);return!1}if("HTMLElement"in self&&"contains"in HTMLElement.prototype)try{delete HTMLElement.prototype.contains}catch(t){}"Node"in self?Node.prototype.contains=e:document.contains=Element.prototype.contains=e}();}if (!(document.isSameNode
)) {!function(){function e(e){if(!(0 in arguments))throw new TypeError("1 argument is required");return this===e}if("HTMLElement"in self&&"isSameNode"in HTMLElement.prototype)try{delete HTMLElement.prototype.isSameNode}catch(t){}"Node"in self?Node.prototype.isSameNode=e:document.isSameNode=Element.prototype.isSameNode=e}();}if (!((function(n){if(!("Event"in n))return!1
try{return new Event("click"),!0}catch(t){return!1}})(self)
)) {!function(){function e(e,t){if(!e)throw new Error("Not enough arguments");var n;if("createEvent"in document){n=document.createEvent("Event");var o=!(!t||t.bubbles===undefined)&&t.bubbles,i=!(!t||t.cancelable===undefined)&&t.cancelable;return n.initEvent(e,o,i),n}return n=document.createEventObject(),n.type=e,n.bubbles=!(!t||t.bubbles===undefined)&&t.bubbles,n.cancelable=!(!t||t.cancelable===undefined)&&t.cancelable,n}var t={click:1,dblclick:1,keyup:1,keypress:1,keydown:1,mousedown:1,mouseup:1,mousemove:1,mouseover:1,mouseenter:1,mouseleave:1,mouseout:1,storage:1,storagecommit:1,textinput:1};if("undefined"!=typeof document&&"undefined"!=typeof window){var n=window.Event&&window.Event.prototype||null;e.NONE=0,e.CAPTURING_PHASE=1,e.AT_TARGET=2,e.BUBBLING_PHASE=3,window.Event=Window.prototype.Event=e,n&&Object.defineProperty(window.Event,"prototype",{configurable:!1,enumerable:!1,writable:!0,value:n}),"createEvent"in document||(window.addEventListener=Window.prototype.addEventListener=Document.prototype.addEventListener=Element.prototype.addEventListener=function o(){var e=this,n=arguments[0],o=arguments[1];if(e===window&&n in t)throw new Error("In IE8 the event: "+n+" is not available on the window object. Please see https://github.com/Financial-Times/polyfill-service/issues/317 for more information.");e._events||(e._events={}),e._events[n]||(e._events[n]=function(t){var n,o=e._events[t.type].list,i=o.slice(),r=-1,c=i.length;for(t.preventDefault=function a(){!1!==t.cancelable&&(t.returnValue=!1)},t.stopPropagation=function l(){t.cancelBubble=!0},t.stopImmediatePropagation=function s(){t.cancelBubble=!0,t.cancelImmediate=!0},t.currentTarget=e,t.relatedTarget=t.fromElement||null,t.target=t.target||t.srcElement||e,t.timeStamp=(new Date).getTime(),t.clientX&&(t.pageX=t.clientX+document.documentElement.scrollLeft,t.pageY=t.clientY+document.documentElement.scrollTop);++r<c&&!t.cancelImmediate;)r in i&&(n=i[r],o.includes(n)&&"function"==typeof n&&n.call(e,t))},e._events[n].list=[],e.attachEvent&&e.attachEvent("on"+n,e._events[n])),e._events[n].list.push(o)},window.removeEventListener=Window.prototype.removeEventListener=Document.prototype.removeEventListener=Element.prototype.removeEventListener=function i(){var e,t=this,n=arguments[0],o=arguments[1];t._events&&t._events[n]&&t._events[n].list&&-1!==(e=t._events[n].list.indexOf(o))&&(t._events[n].list.splice(e,1),t._events[n].list.length||(t.detachEvent&&t.detachEvent("on"+n,t._events[n]),delete t._events[n]))},window.dispatchEvent=Window.prototype.dispatchEvent=Document.prototype.dispatchEvent=Element.prototype.dispatchEvent=function r(e){if(!arguments.length)throw new Error("Not enough arguments");if(!e||"string"!=typeof e.type)throw new Error("DOM Events Exception 0");var t=this,n=e.type;try{if(!e.bubbles){e.cancelBubble=!0;var o=function(e){e.cancelBubble=!0,(t||window).detachEvent("on"+n,o)};this.attachEvent("on"+n,o)}this.fireEvent("on"+n,e)}catch(i){e.target=t;do{e.currentTarget=t,"_events"in t&&"function"==typeof t._events[n]&&t._events[n].call(t,e),"function"==typeof t["on"+n]&&t["on"+n].call(t,e),t=9===t.nodeType?t.parentWindow:t.parentNode}while(t&&!e.cancelBubble)}return!0},document.attachEvent("onreadystatechange",function(){"complete"===document.readyState&&document.dispatchEvent(new e("DOMContentLoaded",{bubbles:!0}))}))}}();}if (!("CustomEvent"in self&&("function"==typeof self.CustomEvent||self.CustomEvent.toString().indexOf("CustomEventConstructor")>-1)
)) {self.CustomEvent=function e(t,n){if(!t)throw Error('TypeError: Failed to construct "CustomEvent": An event name must be provided.');var l;if(n=n||{bubbles:!1,cancelable:!1,detail:null},"createEvent"in document)try{l=document.createEvent("CustomEvent"),l.initCustomEvent(t,n.bubbles,n.cancelable,n.detail)}catch(a){l=document.createEvent("Event"),l.initEvent(t,n.bubbles,n.cancelable),l.detail=n.detail}else l=new Event(t,n),l.detail=n&&n.detail||null;return l},CustomEvent.prototype=Event.prototype;}if (!((function(){var n=!1
return document.documentElement.addEventListener("focusin",function(){n=!0}),document.documentElement.dispatchEvent(new Event("focusin")),n})()
)) {self.addEventListener("focus",function(e){e.target.dispatchEvent(new Event("focusin",{bubbles:!0,cancelable:!0}))},!0),self.addEventListener("blur",function(e){e.target.dispatchEvent(new Event("focusout",{bubbles:!0,cancelable:!0}))},!0);}if (!("onhashchange"in self&&(null==self.onhashchange||"function"==typeof self.onhashchange)
)) {!function(n){function h(){a!==n.location.hash&&(a=n.location.hash,n.dispatchEvent(new Event("hashchange"))),setTimeout(h,500)}var a=n.location.hash;n.onhashchange=function(){},h()}(self);}if (!("XMLHttpRequest"in self&&"prototype"in self.XMLHttpRequest&&"addEventListener"in self.XMLHttpRequest.prototype
)) {!function(e,t){e.XMLHttpRequest=function s(){var e=this,n=e._request=t?new t:new ActiveXObject("MSXML2.XMLHTTP.3.0");n.onreadystatechange=function(){e.readyState=n.readyState;var t=4===e.readyState;e.response=e.responseText=t?n.responseText:null,e.status=t?n.status:null,e.statusText=t?n.statusText:null,e.dispatchEvent(new Event("readystatechange")),t&&e.dispatchEvent(new Event("load"))},"onerror"in n&&(n.onerror=function(){e.dispatchEvent(new Event("error"))})},e.XMLHttpRequest.UNSENT=0,e.XMLHttpRequest.OPENED=1,e.XMLHttpRequest.HEADERS_RECEIVED=2,e.XMLHttpRequest.LOADING=3,e.XMLHttpRequest.DONE=4;var n=e.XMLHttpRequest.prototype;n.addEventListener=e.addEventListener,n.removeEventListener=e.removeEventListener,n.dispatchEvent=e.dispatchEvent,n.abort=function r(){return this._request()},n.getAllResponseHeaders=function u(){return this._request.getAllResponseHeaders()},n.getResponseHeader=function i(e){return this._request.getResponseHeader(e)},n.open=function a(e,t){this._request.open(e,t,arguments[2],arguments[3],arguments[4])},n.overrideMimeType=function o(e){this._request.overrideMimeType(e)},n.send=function p(){this._request.send(0 in arguments?arguments[0]:null)},n.setRequestHeader=function d(e,t){this._request.setRequestHeader(e,t)}}(self,self.XMLHttpRequest);}var _mutation=function(){function e(e){return"function"==typeof Node?e instanceof Node:e&&"object"==typeof e&&e.nodeName&&e.nodeType>=1&&e.nodeType<=12}return function n(t){if(1===t.length)return e(t[0])?t[0]:document.createTextNode(t[0]+"");for(var o=document.createDocumentFragment(),r=0;r<t.length;r++)o.appendChild(e(t[r])?t[r]:document.createTextNode(t[r]+""));return o}}();if (!("DocumentFragment"in self&&"append"in DocumentFragment.prototype
)) {!function(t){document.createDocumentFragment().constructor.prototype.append=function n(){this.appendChild(_mutation(arguments))},t.DocumentFragment.prototype.append=function e(){this.appendChild(_mutation(arguments))}}(self);}if (!("DocumentFragment"in self&&"prepend"in DocumentFragment.prototype
)) {!function(t){document.createDocumentFragment().constructor.prototype.prepend=function e(){this.insertBefore(_mutation(arguments),this.firstChild)},t.DocumentFragment.prototype.prepend=function n(){this.insertBefore(_mutation(arguments),this.firstChild)}}(self);}if (!("Element"in self&&"after"in Element.prototype
)) {Document.prototype.after=Element.prototype.after=function t(){if(this.parentNode){for(var t=Array.prototype.slice.call(arguments),e=this.nextSibling,o=e?t.indexOf(e):-1;-1!==o&&(e=e.nextSibling);)o=t.indexOf(e);this.parentNode.insertBefore(_mutation(arguments),e)}},"Text"in self&&(Text.prototype.after=Element.prototype.after);}if (!("Element"in self&&"append"in Element.prototype
)) {Document.prototype.append=Element.prototype.append=function p(){this.appendChild(_mutation(arguments))};}if (!("Element"in self&&"before"in Element.prototype
)) {Document.prototype.before=Element.prototype.before=function e(){if(this.parentNode){for(var e=Array.prototype.slice.call(arguments),t=this.previousSibling,o=t?e.indexOf(t):-1;-1!==o&&(t=t.previousSibling);)o=e.indexOf(t);this.parentNode.insertBefore(_mutation(arguments),t?t.nextSibling:this.parentNode.firstChild)}},"Text"in self&&(Text.prototype.before=Element.prototype.before);}if (!("Element"in self&&"prepend"in Element.prototype
)) {Document.prototype.prepend=Element.prototype.prepend=function t(){this.insertBefore(_mutation(arguments),this.firstChild)};}if (!("Element"in self&&"remove"in Element.prototype
)) {Document.prototype.remove=Element.prototype.remove=function e(){this.parentNode&&this.parentNode.removeChild(this)},"Text"in self&&(Text.prototype.remove=Element.prototype.remove);}if (!("Element"in self&&"replaceWith"in Element.prototype
)) {Document.prototype.replaceWith=Element.prototype.replaceWith=function e(){this.parentNode&&this.parentNode.replaceChild(_mutation(arguments),this)},"Text"in self&&(Text.prototype.replaceWith=Element.prototype.replaceWith);}if (!("document"in self&&"querySelector"in self.document
)) {!function(){function e(e,n,o){var l,u,c=document.createElement("div"),i="qsa"+String(Math.random()).slice(3);return c.innerHTML="x<style>"+n+"{qsa:"+i+";}",l=r.appendChild(c.lastChild),u=t(e,n,o,i),r.removeChild(l),o?u[0]:u}function t(e,r,n,o){var l,u=/1|9/.test(e.nodeType),c=e.childNodes,i=[],a=-1;if(u&&e.currentStyle&&e.currentStyle.qsa===o&&i.push(e)&&n)return i;for(;l=c[++a];)if(i=i.concat(t(l,r,n,o)),n&&i.length)return i;return i}var r=document.getElementsByTagName("head")[0];Document.prototype.querySelector=Element.prototype.querySelector=function n(t){return e(this,t,!0)},Document.prototype.querySelectorAll=Element.prototype.querySelectorAll=function o(t){return e(this,t,!1)}}();}if (!((function(){if(!document.documentElement.dataset)return!1
var t=document.createElement("div")
return t.setAttribute("data-a-b","c"),t.dataset&&"c"==t.dataset.aB})()
)) {Object.defineProperty(Element.prototype,"dataset",{get:function(){for(var t=this,e=this.attributes,r={},n=0;n<e.length;n++){var a=e[n];if(a&&a.name&&/^data-\w[\w-]*$/.test(a.name)){var i=a.name,u=a.value,s=i.substr(5).replace(/-./g,function(t){return t.charAt(1).toUpperCase()});Object.defineProperty(r,s,{enumerable:!0,get:function(){return this.value}.bind({value:u||""}),set:function o(t,e){void 0!==e?this.setAttribute(t,e):this.removeAttribute(t)}.bind(t,i)})}}return r}});}if (!("document"in self&&"matches"in document.documentElement
)) {Element.prototype.matches=Element.prototype.webkitMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.mozMatchesSelector||function e(t){for(var o=this,r=(o.document||o.ownerDocument).querySelectorAll(t),c=0;r[c]&&r[c]!==o;)++c;return!!r[c]};}if (!("document"in self&&"closest"in document.documentElement
)) {Element.prototype.closest=function e(n){for(var t=this;t;){if(t.matches(n))return t;t="SVGElement"in window&&t instanceof SVGElement?t.parentNode:t.parentElement}return null};}if (!("Element"in self&&"inert"in Element.prototype
)) {!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t():"function"==typeof define&&define.amd?define("inert",t):t()}(0,function(){"use strict";function e(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function t(e,n,i){if(e.nodeType==Node.ELEMENT_NODE){var o=e;n&&n(o);var r=o.shadowRoot;if(r)return void t(r,n,r);if("content"==o.localName){for(var s=o,a=s.getDistributedNodes?s.getDistributedNodes():[],d=0;d<a.length;d++)t(a[d],n,i);return}if("slot"==o.localName){for(var u=o,h=u.assignedNodes?u.assignedNodes({flatten:!0}):[],c=0;c<h.length;c++)t(h[c],n,i);return}}for(var l=e.firstChild;null!=l;)t(l,n,i),l=l.nextSibling}function n(e){if(!e.querySelector("style#inert-style")){var t=document.createElement("style");t.setAttribute("id","inert-style"),t.textContent="\n[inert] {\n  pointer-events: none;\n  cursor: default;\n}\n\n[inert], [inert] * {\n  user-select: none;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n}\n",e.appendChild(t)}}var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),o=Array.prototype.slice,r=Element.prototype.matches||Element.prototype.msMatchesSelector,s=["a[href]","area[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","details","summary","iframe","object","embed","[contenteditable]"].join(","),a=function(){function n(t,i){e(this,n),this._inertManager=i,this._rootElement=t,this._managedNodes=new Set,this._rootElement.hasAttribute("aria-hidden")?this._savedAriaHidden=this._rootElement.getAttribute("aria-hidden"):this._savedAriaHidden=null,this._rootElement.setAttribute("aria-hidden","true"),this._makeSubtreeUnfocusable(this._rootElement),this._observer=new MutationObserver(this._onMutation.bind(this)),this._observer.observe(this._rootElement,{attributes:!0,childList:!0,subtree:!0})}return i(n,[{key:"destructor",value:function a(){this._observer.disconnect(),this._rootElement&&(null!==this._savedAriaHidden?this._rootElement.setAttribute("aria-hidden",this._savedAriaHidden):this._rootElement.removeAttribute("aria-hidden")),this._managedNodes.forEach(function(e){this._unmanageNode(e.node)},this),this._observer=null,this._rootElement=null,this._managedNodes=null,this._inertManager=null}},{key:"_makeSubtreeUnfocusable",value:function d(e){var n=this;t(e,function(e){return n._visitNode(e)});var i=document.activeElement;if(!document.body.contains(e)){for(var o=e,r=undefined;o;){if(o.nodeType===Node.DOCUMENT_FRAGMENT_NODE){r=o;break}o=o.parentNode}r&&(i=r.activeElement)}e.contains(i)&&(i.blur(),i===document.activeElement&&document.body.focus())}},{key:"_visitNode",value:function u(e){if(e.nodeType===Node.ELEMENT_NODE){var t=e;t!==this._rootElement&&t.hasAttribute("inert")&&this._adoptInertRoot(t),(r.call(t,s)||t.hasAttribute("tabindex"))&&this._manageNode(t)}}},{key:"_manageNode",value:function h(e){var t=this._inertManager.register(e,this);this._managedNodes.add(t)}},{key:"_unmanageNode",value:function c(e){var t=this._inertManager.deregister(e,this);t&&this._managedNodes["delete"](t)}},{key:"_unmanageSubtree",value:function l(e){var n=this;t(e,function(e){return n._unmanageNode(e)})}},{key:"_adoptInertRoot",value:function f(e){var t=this._inertManager.getInertRoot(e);t||(this._inertManager.setInert(e,!0),t=this._inertManager.getInertRoot(e)),t.managedNodes.forEach(function(e){this._manageNode(e.node)},this)}},{key:"_onMutation",value:function _(e,t){e.forEach(function(e){var t=e.target;if("childList"===e.type)o.call(e.addedNodes).forEach(function(e){this._makeSubtreeUnfocusable(e)},this),o.call(e.removedNodes).forEach(function(e){this._unmanageSubtree(e)},this);else if("attributes"===e.type)if("tabindex"===e.attributeName)this._manageNode(t);else if(t!==this._rootElement&&"inert"===e.attributeName&&t.hasAttribute("inert")){this._adoptInertRoot(t);var n=this._inertManager.getInertRoot(t);this._managedNodes.forEach(function(e){t.contains(e.node)&&n._manageNode(e.node)})}},this)}},{key:"managedNodes",get:function b(){return new Set(this._managedNodes)}},{key:"hasSavedAriaHidden",get:function v(){return null!==this._savedAriaHidden}},{key:"savedAriaHidden",set:function m(e){this._savedAriaHidden=e},get:function y(){return this._savedAriaHidden}}]),n}(),d=function(){function t(n,i){e(this,t),this._node=n,this._overrodeFocusMethod=!1,this._inertRoots=new Set([i]),this._savedTabIndex=null,this._destroyed=!1,this.ensureUntabbable()}return i(t,[{key:"destructor",value:function n(){if(this._throwIfDestroyed(),this._node&&this._node.nodeType===Node.ELEMENT_NODE){var e=this._node;null!==this._savedTabIndex?e.setAttribute("tabindex",this._savedTabIndex):e.removeAttribute("tabindex"),this._overrodeFocusMethod&&delete e.focus}this._node=null,this._inertRoots=null,this._destroyed=!0}},{key:"_throwIfDestroyed",value:function o(){if(this.destroyed)throw new Error("Trying to access destroyed InertNode")}},{key:"ensureUntabbable",value:function a(){if(this.node.nodeType===Node.ELEMENT_NODE){var e=this.node;if(r.call(e,s)){if(-1===e.tabIndex&&this.hasSavedTabIndex)return;e.hasAttribute("tabindex")&&(this._savedTabIndex=e.tabIndex),e.setAttribute("tabindex","-1"),e.nodeType===Node.ELEMENT_NODE&&(e.focus=function(){},this._overrodeFocusMethod=!0)}else e.hasAttribute("tabindex")&&(this._savedTabIndex=e.tabIndex,e.removeAttribute("tabindex"))}}},{key:"addInertRoot",value:function d(e){this._throwIfDestroyed(),this._inertRoots.add(e)}},{key:"removeInertRoot",value:function u(e){this._throwIfDestroyed(),this._inertRoots["delete"](e),0===this._inertRoots.size&&this.destructor()}},{key:"destroyed",get:function h(){return this._destroyed}},{key:"hasSavedTabIndex",get:function c(){return null!==this._savedTabIndex}},{key:"node",get:function l(){return this._throwIfDestroyed(),this._node}},{key:"savedTabIndex",set:function f(e){this._throwIfDestroyed(),this._savedTabIndex=e},get:function _(){return this._throwIfDestroyed(),this._savedTabIndex}}]),t}(),u=function(){function t(i){if(e(this,t),!i)throw new Error("Missing required argument; InertManager needs to wrap a document.");this._document=i,this._managedNodes=new Map,this._inertRoots=new Map,this._observer=new MutationObserver(this._watchForInert.bind(this)),n(i.head||i.body||i.documentElement),"loading"===i.readyState?i.addEventListener("DOMContentLoaded",this._onDocumentLoaded.bind(this)):this._onDocumentLoaded()}return i(t,[{key:"setInert",value:function s(e,t){if(t){if(this._inertRoots.has(e))return;var i=new a(e,this);if(e.setAttribute("inert",""),this._inertRoots.set(e,i),!this._document.body.contains(e))for(var o=e.parentNode;o;)11===o.nodeType&&n(o),o=o.parentNode}else{if(!this._inertRoots.has(e))return;this._inertRoots.get(e).destructor(),this._inertRoots["delete"](e),e.removeAttribute("inert")}}},{key:"getInertRoot",value:function u(e){return this._inertRoots.get(e)}},{key:"register",value:function h(e,t){var n=this._managedNodes.get(e);return n!==undefined?n.addInertRoot(t):n=new d(e,t),this._managedNodes.set(e,n),n}},{key:"deregister",value:function c(e,t){var n=this._managedNodes.get(e);return n?(n.removeInertRoot(t),n.destroyed&&this._managedNodes["delete"](e),n):null}},{key:"_onDocumentLoaded",value:function l(){o.call(this._document.querySelectorAll("[inert]")).forEach(function(e){this.setInert(e,!0)},this),this._observer.observe(this._document.body||this._document.documentElement,{attributes:!0,subtree:!0,childList:!0})}},{key:"_watchForInert",value:function f(e,t){var n=this;e.forEach(function(e){switch(e.type){case"childList":o.call(e.addedNodes).forEach(function(e){if(e.nodeType===Node.ELEMENT_NODE){var t=o.call(e.querySelectorAll("[inert]"));r.call(e,"[inert]")&&t.unshift(e),t.forEach(function(e){this.setInert(e,!0)},n)}},n);break;case"attributes":if("inert"!==e.attributeName)return;var t=e.target,i=t.hasAttribute("inert");n.setInert(t,i)}},this)}}]),t}(),h=new u(document);Element.prototype.hasOwnProperty("inert")||Object.defineProperty(Element.prototype,"inert",{enumerable:!0,get:function c(){return this.hasAttribute("inert")},set:function l(e){h.setInert(this,e)}})});}if (!("document"in self&&"placeholder"in document.createElement("input")
)) {Object.defineProperty(Element.prototype,"placeholder",{get:function(){return this.getAttribute("placeholder")},set:function(e){if(e&&/^(input|textarea)$/i.test(this.nodeName)&&(!/^(input)$/i.test(this.nodeName)||/^(email|number|password|search|tel|text|url|)$/i.test(this.getAttribute("type")))){var t=this,n=document.createElement("ms-input"),i=n.appendChild(document.createElement("ms-placeholder")),o=n.runtimeStyle,a=i.runtimeStyle,r=t.currentStyle;i.appendChild(document.createTextNode(e)),o.display="inline-block",o.fontSize=r.fontSize,o.margin=r.margin,o.width=r.width,t.parentNode.insertBefore(n,t).appendChild(t),a.backgroundColor="transparent",a.fontFamily=r.fontFamily,a.fontSize=r.fontSize,a.fontWeight=r.fontWeight,a.margin="2px 0 0 2px",a.padding=r.padding,a.position="absolute",a.display=t.value?"none":"inline-block",t.runtimeStyle.margin="0",i.attachEvent("onclick",function(){t.focus()}),t.attachEvent("onkeypress",function(){a.display="none"}),t.attachEvent("onkeyup",function(){a.display=t.value?"none":"inline-block"}),Object.defineProperty(t,"placeholder",{get:function(){return i.innerHTML},set:function(e){i.innerHTML=e}})}}}),document.attachEvent("onreadystatechange",function(){if("complete"===document.readyState)for(var e=document.querySelectorAll("input,textarea"),t=0,n=e.length;t<n;++t)e[t].placeholder&&(e[t].placeholder=e[t].placeholder)});}if (!("HTMLSelectElement"in self&&"selectedOptions"in self.HTMLSelectElement.prototype
)) {!function(e){Object.defineProperty(e.HTMLSelectElement.prototype,"selectedOptions",{get:function(){var e=this.querySelectorAll("option:checked");if(!this.multiple&&0===e.length){var t=this.querySelectorAll("option");if(t.length>0){var r=t[0],l="o"+Math.floor(9e6*Math.random())+1e6;r.setAttribute(l,""),e=this.querySelectorAll("option["+l+"]"),r.removeAttribute(l)}}if(!this.multiple&&e.length>1){var i=e[e.length-1],n="o"+Math.floor(9e6*Math.random())+1e6;i.setAttribute(n,""),e=this.querySelectorAll("option:checked["+n+"]"),i.removeAttribute(n)}for(var o=0;o<e.length;o++){var u=e[o];u.id&&(e[u.id]||(e[u.id]=u))}return e.namedItem=function h(t){if("string"!=typeof t)return null;if(""===t)return null;for(var r=0;r<e.length;r++){var l=e[r];if(t===l.id)return l}return null},e},enumerable:!0,configurable:!0})}(self);}if (!("HTMLTemplateElement"in self
)) {!function(){"use strict";function e(e,t){if(!e.childNodes.length)return[];switch(e.nodeType){case Node.DOCUMENT_NODE:return f.call(e,t);case Node.DOCUMENT_FRAGMENT_NODE:return h.call(e,t);default:return s.call(e,t)}}var t="undefined"==typeof HTMLTemplateElement,o=!(document.createDocumentFragment().cloneNode()instanceof DocumentFragment),n=!1;/Trident/.test(navigator.userAgent)&&function(){function e(e,t){if(e instanceof DocumentFragment)for(var n;n=e.firstChild;)o.call(this,n,t);else o.call(this,e,t);return e}n=!0;var t=Node.prototype.cloneNode;Node.prototype.cloneNode=function i(e){var o=t.call(this,e);return this instanceof DocumentFragment&&(o.__proto__=DocumentFragment.prototype),o},DocumentFragment.prototype.querySelectorAll=HTMLElement.prototype.querySelectorAll,DocumentFragment.prototype.querySelector=HTMLElement.prototype.querySelector,Object.defineProperties(DocumentFragment.prototype,{nodeType:{get:function(){return Node.DOCUMENT_FRAGMENT_NODE},configurable:!0},localName:{get:function(){return undefined},configurable:!0},nodeName:{get:function(){return"#document-fragment"},configurable:!0}});var o=Node.prototype.insertBefore;Node.prototype.insertBefore=e;var r=Node.prototype.appendChild;Node.prototype.appendChild=function p(t){return t instanceof DocumentFragment?e.call(this,t,null):r.call(this,t),t};var c=Node.prototype.removeChild,a=Node.prototype.replaceChild;Node.prototype.replaceChild=function u(t,o){return t instanceof DocumentFragment?(e.call(this,t,o),c.call(this,o)):a.call(this,t,o),o},Document.prototype.createDocumentFragment=function d(){var e=this.createElement("df");return e.__proto__=DocumentFragment.prototype,e};var l=Document.prototype.importNode;Document.prototype.importNode=function m(e,t){t=t||!1;var o=l.call(this,e,t);return e instanceof DocumentFragment&&(o.__proto__=DocumentFragment.prototype),o}}();var r=Node.prototype.cloneNode,c=Document.prototype.createElement,a=Document.prototype.importNode,l=Node.prototype.removeChild,i=Node.prototype.appendChild,p=Node.prototype.replaceChild,u=DOMParser.prototype.parseFromString,d=Object.getOwnPropertyDescriptor(window.HTMLElement.prototype,"innerHTML")||{get:function(){return this.innerHTML},set:function(e){this.innerHTML=e}},m=Object.getOwnPropertyDescriptor(window.Node.prototype,"childNodes")||{get:function(){return this.childNodes}},s=Element.prototype.querySelectorAll,f=Document.prototype.querySelectorAll,h=DocumentFragment.prototype.querySelectorAll,y=function(){if(!t){var e=document.createElement("template"),n=document.createElement("template");n.content.appendChild(document.createElement("div")),e.content.appendChild(n);var r=e.cloneNode(!0);return 0===r.content.childNodes.length||0===r.content.firstChild.content.childNodes.length||o}}(),N=function(){};if(t){var g=document.implementation.createHTMLDocument("template"),v=!0,D=document.createElement("style");D.textContent="template{display:none;}";var E=document.head;E.insertBefore(D,E.firstElementChild),N.prototype=Object.create(HTMLElement.prototype);var b=!document.createElement("div").hasOwnProperty("innerHTML");N.decorate=function(e){if(!e.content&&e.namespaceURI===document.documentElement.namespaceURI){e.content=g.createDocumentFragment();for(var t;t=e.firstChild;)i.call(e.content,t);if(b)e.__proto__=N.prototype;else if(e.cloneNode=function(e){return N._cloneNode(this,e)},v)try{C(e),_(e)}catch(o){v=!1}N.bootstrap(e.content)}};var T={option:["select"],thead:["table"],col:["colgroup","table"],tr:["tbody","table"],th:["tr","tbody","table"],td:["tr","tbody","table"]},M=function(e){return(/<([a-z][^/\0>\x20\t\r\n\f]+)/i.exec(e)||["",""])[1].toLowerCase()},C=function k(e){Object.defineProperty(e,"innerHTML",{get:function(){return q(this)},set:function(e){var t=T[M(e)];if(t)for(var o=0;o<t.length;o++)e="<"+t[o]+">"+e+"</"+t[o]+">";for(g.body.innerHTML=e,N.bootstrap(g);this.content.firstChild;)l.call(this.content,this.content.firstChild);var n=g.body;if(t)for(var r=0;r<t.length;r++)n=n.lastChild;for(;n.firstChild;)i.call(this.content,n.firstChild)},configurable:!0})},_=function B(e){Object.defineProperty(e,"outerHTML",{get:function(){return"<template>"+this.innerHTML+"</template>"},set:function(e){if(!this.parentNode)throw new Error("Failed to set the 'outerHTML' property on 'Element': This element has no parent node.");g.body.innerHTML=e;for(var t=this.ownerDocument.createDocumentFragment();g.body.firstChild;)i.call(t,g.body.firstChild);p.call(this.parentNode,t,this)},configurable:!0})};C(N.prototype),_(N.prototype),N.bootstrap=function G(t){for(var o,n=e(t,"template"),r=0,c=n.length;r<c&&(o=n[r]);r++)N.decorate(o)},document.addEventListener("DOMContentLoaded",function(){N.bootstrap(document)}),Document.prototype.createElement=function z(){var e=c.apply(this,arguments);return"template"===e.localName&&N.decorate(e),e},DOMParser.prototype.parseFromString=function(){var e=u.apply(this,arguments);return N.bootstrap(e),e},Object.defineProperty(HTMLElement.prototype,"innerHTML",{get:function(){return q(this)},set:function(e){d.set.call(this,e),N.bootstrap(this)},configurable:!0,enumerable:!0});var F=/[&\u00A0"]/g,L=/[&\u00A0<>]/g,O=function(e){switch(e){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case'"':return"&quot;";case" ":return"&nbsp;"}},w=function(e){return e.replace(F,O)},H=function(e){return e.replace(L,O)},A=function(e){for(var t={},o=0;o<e.length;o++)t[e[o]]=!0;return t},x=A(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),S=A(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]),j=function(e,t,o){switch(e.nodeType){case Node.ELEMENT_NODE:for(var n,r=e.localName,c="<"+r,a=e.attributes,l=0;n=a[l];l++)c+=" "+n.name+'="'+w(n.value)+'"';return c+=">",x[r]?c:c+q(e,o)+"</"+r+">";case Node.TEXT_NODE:var i=e.data;return t&&S[t.localName]?i:H(i);case Node.COMMENT_NODE:return"\x3c!--"+e.data+"--\x3e";default:throw window.console.error(e),new Error("not implemented")}},q=function(e,t){"template"===e.localName&&(e=e.content);for(var o,n="",r=t?t(e):m.get.call(e),c=0,a=r.length;c<a&&(o=r[c]);c++)n+=j(o,e,t);return n}}if(t||y){N._cloneNode=function X(e,t){var o=r.call(e,!1);return this.decorate&&this.decorate(o),t&&(i.call(o.content,r.call(e.content,!0)),P(o.content,e.content)),o};var P=function J(t,o){if(o.querySelectorAll){var n=e(o,"template");if(0!==n.length)for(var r,c,a=e(t,"template"),l=0,i=a.length;l<i;l++)c=n[l],r=a[l],N&&N.decorate&&N.decorate(c),p.call(r.parentNode,R.call(c,!0),r)}},U=function K(t){for(var o,n,r=e(t,'script:not([type]),script[type="application/javascript"],script[type="text/javascript"]'),a=0;a<r.length;a++){n=r[a],o=c.call(document,"script"),o.textContent=n.textContent;for(var l,i=n.attributes,u=0;u<i.length;u++)l=i[u],o.setAttribute(l.name,l.value);p.call(n.parentNode,o,n)}},R=Node.prototype.cloneNode=function Q(e){var t;if(!n&&o&&this instanceof DocumentFragment){if(!e)return this.ownerDocument.createDocumentFragment();t=I.call(this.ownerDocument,this,!0)}else t=this.nodeType===Node.ELEMENT_NODE&&"template"===this.localName&&this.namespaceURI==document.documentElement.namespaceURI?N._cloneNode(this,e):r.call(this,e);return e&&P(t,this),t},I=Document.prototype.importNode=function V(e,t){if(t=t||!1,"template"===e.localName)return N._cloneNode(e,t);var o=a.call(this,e,t);return t&&(P(o,e),U(o)),o}}t&&(window.HTMLTemplateElement=N)}();}if (!("document"in self&&"hidden"in document&&"visibilityState"in document
)) {!function(){function i(){document.hidden=document[t+"Hidden"],document.visibilityState=document[t+"VisibilityState"]}var t=document.webkitVisibilityState?"webkit":document.mozVisibilityState?"moz":null;t&&(i(),document.addEventListener(t+"visibilitychange",function(){i(),document.dispatchEvent(new CustomEvent("visibilitychange"))}))}();}if (!("fetch"in self&&"Request"in self&&function(){try{return"signal"in new Request("")}catch(e){return!1}}()
)) {!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.WHATWGFetch={})}(this,function(t){"use strict";function e(t){return t&&DataView.prototype.isPrototypeOf(t)}function r(t){if("string"!=typeof t&&(t=String(t)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(t)||""===t)throw new TypeError('Invalid character in header field name: "'+t+'"');return t.toLowerCase()}function o(t){return"string"!=typeof t&&(t=String(t)),t}function n(t){var e={next:function(){var e=t.shift();return{done:e===undefined,value:e}}};return E.iterable&&(e[Symbol.iterator]=function(){return e}),e}function i(t){this.map={},t instanceof i?t.forEach(function(t,e){this.append(e,t)},this):Array.isArray(t)?t.forEach(function(t){this.append(t[0],t[1])},this):t&&Object.getOwnPropertyNames(t).forEach(function(e){this.append(e,t[e])},this)}function s(t){if(t.bodyUsed)return Promise.reject(new TypeError("Already read"));t.bodyUsed=!0}function a(t){return new Promise(function(e,r){t.onload=function(){e(t.result)},t.onerror=function(){r(t.error)}})}function f(t){var e=new FileReader,r=a(e);return e.readAsArrayBuffer(t),r}function u(t){var e=new FileReader,r=a(e);return e.readAsText(t),r}function h(t){for(var e=new Uint8Array(t),r=new Array(e.length),o=0;o<e.length;o++)r[o]=String.fromCharCode(e[o]);return r.join("")}function c(t){if(t.slice)return t.slice(0);var e=new Uint8Array(t.byteLength);return e.set(new Uint8Array(t)),e.buffer}function d(){return this.bodyUsed=!1,this._initBody=function(t){this.bodyUsed=this.bodyUsed,this._bodyInit=t,t?"string"==typeof t?this._bodyText=t:E.blob&&Blob.prototype.isPrototypeOf(t)?this._bodyBlob=t:E.formData&&FormData.prototype.isPrototypeOf(t)?this._bodyFormData=t:E.searchParams&&URLSearchParams.prototype.isPrototypeOf(t)?this._bodyText=t.toString():E.arrayBuffer&&E.blob&&e(t)?(this._bodyArrayBuffer=c(t.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):E.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(t)||A(t))?this._bodyArrayBuffer=c(t):this._bodyText=t=Object.prototype.toString.call(t):this._bodyText="",this.headers.get("content-type")||("string"==typeof t?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):E.searchParams&&URLSearchParams.prototype.isPrototypeOf(t)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},E.blob&&(this.blob=function(){var t=s(this);if(t)return t;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){if(this._bodyArrayBuffer){var t=s(this);return t||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}return this.blob().then(f)}),this.text=function(){var t=s(this);if(t)return t;if(this._bodyBlob)return u(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(h(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},E.formData&&(this.formData=function(){return this.text().then(l)}),this.json=function(){return this.text().then(JSON.parse)},this}function y(t){var e=t.toUpperCase();return _.indexOf(e)>-1?e:t}function p(t,e){if(!(this instanceof p))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');e=e||{};var r=e.body;if(t instanceof p){if(t.bodyUsed)throw new TypeError("Already read");this.url=t.url,this.credentials=t.credentials,e.headers||(this.headers=new i(t.headers)),this.method=t.method,this.mode=t.mode,this.signal=t.signal,r||null==t._bodyInit||(r=t._bodyInit,t.bodyUsed=!0)}else this.url=String(t);if(this.credentials=e.credentials||this.credentials||"same-origin",!e.headers&&this.headers||(this.headers=new i(e.headers)),this.method=y(e.method||this.method||"GET"),this.mode=e.mode||this.mode||null,this.signal=e.signal||this.signal,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&r)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(r),!("GET"!==this.method&&"HEAD"!==this.method||"no-store"!==e.cache&&"no-cache"!==e.cache)){var o=/([?&])_=[^&]*/;if(o.test(this.url))this.url=this.url.replace(o,"$1_="+(new Date).getTime());else{var n=/\?/;this.url+=(n.test(this.url)?"&":"?")+"_="+(new Date).getTime()}}}function l(t){var e=new FormData;return t.trim().split("&").forEach(function(t){if(t){var r=t.split("="),o=r.shift().replace(/\+/g," "),n=r.join("=").replace(/\+/g," ");e.append(decodeURIComponent(o),decodeURIComponent(n))}}),e}function b(t){var e=new i;return t.replace(/\r?\n[\t ]+/g," ").split("\r").map(function(t){return 0===t.indexOf("\n")?t.substr(1,t.length):t}).forEach(function(t){var r=t.split(":"),o=r.shift().trim();if(o){var n=r.join(":").trim();e.append(o,n)}}),e}function m(t,e){if(!(this instanceof m))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');e||(e={}),this.type="default",this.status=e.status===undefined?200:e.status,this.ok=this.status>=200&&this.status<300,this.statusText=e.statusText===undefined?"":""+e.statusText,this.headers=new i(e.headers),this.url=e.url||"",this._initBody(t)}function w(e,r){return new Promise(function(n,s){function a(){u.abort()}var f=new p(e,r);if(f.signal&&f.signal.aborted)return s(new t.DOMException("Aborted","AbortError"));var u=new XMLHttpRequest;u.onload=function(){var t={status:u.status,statusText:u.statusText,headers:b(u.getAllResponseHeaders()||"")};t.url="responseURL"in u?u.responseURL:t.headers.get("X-Request-URL");var e="response"in u?u.response:u.responseText;setTimeout(function(){n(new m(e,t))},0)},u.onerror=function(){setTimeout(function(){s(new TypeError("Network request failed"))},0)},u.ontimeout=function(){setTimeout(function(){s(new TypeError("Network request failed"))},0)},u.onabort=function(){setTimeout(function(){s(new t.DOMException("Aborted","AbortError"))},0)},u.open(f.method,function h(t){try{return""===t&&v.location.href?v.location.href:t}catch(e){return t}}(f.url),!0),"include"===f.credentials?u.withCredentials=!0:"omit"===f.credentials&&(u.withCredentials=!1),"responseType"in u&&(E.blob?u.responseType="blob":E.arrayBuffer&&f.headers.get("Content-Type")&&-1!==f.headers.get("Content-Type").indexOf("application/octet-stream")&&(u.responseType="arraybuffer")),!r||"object"!=typeof r.headers||r.headers instanceof i?f.headers.forEach(function(t,e){u.setRequestHeader(e,t)}):Object.getOwnPropertyNames(r.headers).forEach(function(t){u.setRequestHeader(t,o(r.headers[t]))}),f.signal&&(f.signal.addEventListener("abort",a),u.onreadystatechange=function(){4===u.readyState&&f.signal.removeEventListener("abort",a)}),u.send("undefined"==typeof f._bodyInit?null:f._bodyInit)})}var v="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof self&&self||void 0!==v&&v,E={searchParams:"URLSearchParams"in v,iterable:"Symbol"in v&&"iterator"in Symbol,blob:"FileReader"in v&&"Blob"in v&&function(){try{return new Blob,!0}catch(t){return!1}}(),formData:"FormData"in v,arrayBuffer:"ArrayBuffer"in v};if(E.arrayBuffer)var T=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],A=ArrayBuffer.isView||function(t){return t&&T.indexOf(Object.prototype.toString.call(t))>-1};i.prototype.append=function(t,e){t=r(t),e=o(e);var n=this.map[t];this.map[t]=n?n+", "+e:e},i.prototype["delete"]=function(t){delete this.map[r(t)]},i.prototype.get=function(t){return t=r(t),this.has(t)?this.map[t]:null},i.prototype.has=function(t){return this.map.hasOwnProperty(r(t))},i.prototype.set=function(t,e){this.map[r(t)]=o(e)},i.prototype.forEach=function(t,e){for(var r in this.map)this.map.hasOwnProperty(r)&&t.call(e,this.map[r],r,this)},i.prototype.keys=function(){var t=[];return this.forEach(function(e,r){t.push(r)}),n(t)},i.prototype.values=function(){var t=[];return this.forEach(function(e){t.push(e)}),n(t)},i.prototype.entries=function(){var t=[];return this.forEach(function(e,r){t.push([r,e])}),n(t)},E.iterable&&(i.prototype[Symbol.iterator]=i.prototype.entries);var _=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];p.prototype.clone=function(){return new p(this,{body:this._bodyInit})},d.call(p.prototype),d.call(m.prototype),m.prototype.clone=function(){return new m(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new i(this.headers),url:this.url})},m.error=function(){var t=new m(null,{status:0,statusText:""});return t.type="error",t};var g=[301,302,303,307,308];m.redirect=function(t,e){if(-1===g.indexOf(e))throw new RangeError("Invalid status code");return new m(null,{status:e,headers:{location:t}})},t.DOMException=v.DOMException;try{new t.DOMException}catch(B){t.DOMException=function(t,e){this.message=t,this.name=e;var r=Error(t);this.stack=r.stack},t.DOMException.prototype=Object.create(Error.prototype),t.DOMException.prototype.constructor=t.DOMException}w.polyfill=!0,v.fetch=w,v.Headers=i,v.Request=p,v.Response=m,t.Headers=i,t.Request=p,t.Response=m,t.fetch=w,Object.defineProperty(t,"__esModule",{value:!0})});}if (!("getComputedStyle"in self
)) {!function(t){function e(t,o,r){var n,i=t.document&&t.currentStyle[o].match(/([\d.]+)(%|cm|em|in|mm|pc|pt|)/)||[0,0,""],l=i[1],u=i[2];return r=r?/%|em/.test(u)&&t.parentElement?e(t.parentElement,"fontSize",null):16:r,n="fontSize"==o?r:/width/i.test(o)?t.clientWidth:t.clientHeight,"%"==u?l/100*n:"cm"==u?.3937*l*96:"em"==u?l*r:"in"==u?96*l:"mm"==u?.3937*l*96/10:"pc"==u?12*l*96/72:"pt"==u?96*l/72:l}function o(t,e){var o="border"==e?"Width":"",r=e+"Top"+o,n=e+"Right"+o,i=e+"Bottom"+o,l=e+"Left"+o;t[e]=(t[r]==t[n]&&t[r]==t[i]&&t[r]==t[l]?[t[r]]:t[r]==t[i]&&t[l]==t[n]?[t[r],t[n]]:t[l]==t[n]?[t[r],t[n],t[i]]:[t[r],t[n],t[i],t[l]]).join(" ")}function r(t){var r,n=this,i=t.currentStyle,l=e(t,"fontSize"),u=function(t){return"-"+t.toLowerCase()};for(r in i)if(Array.prototype.push.call(n,"styleFloat"==r?"float":r.replace(/[A-Z]/,u)),"width"==r)n[r]=t.offsetWidth+"px";else if("height"==r)n[r]=t.offsetHeight+"px";else if("styleFloat"==r)n["float"]=i[r];else if(/margin.|padding.|border.+W/.test(r)&&"auto"!=n[r])n[r]=Math.round(e(t,r,l))+"px";else if(/^outline/.test(r))try{n[r]=i[r]}catch(c){n.outlineColor=i.color,n.outlineStyle=n.outlineStyle||"none",n.outlineWidth=n.outlineWidth||"0px",n.outline=[n.outlineColor,n.outlineWidth,n.outlineStyle].join(" ")}else n[r]=i[r];o(n,"margin"),o(n,"padding"),o(n,"border"),n.fontSize=Math.round(l)+"px"}r.prototype={constructor:r,getPropertyPriority:function(){throw new Error("NotSupportedError: DOM Exception 9")},getPropertyValue:function(t){return this[t.replace(/-\w/g,function(t){return t[1].toUpperCase()})]},item:function(t){return this[t]},removeProperty:function(){throw new Error("NoModificationAllowedError: DOM Exception 7")},setProperty:function(){throw new Error("NoModificationAllowedError: DOM Exception 7")},getPropertyCSSValue:function(){throw new Error("NotSupportedError: DOM Exception 9")}},t.getComputedStyle=function n(t){return new r(t)}}(self);}if (!("location"in self&&"origin"in self.location
)) {try{Object.defineProperty(window.location,"origin",{enumerable:!0,writable:!1,value:window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:""),configurable:!1})}catch(e){window.location.origin=window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:"")}}if (!("requestAnimationFrame"in self
)) {!function(n){var e,t=Date.now(),o=function(){return n.performance&&"function"==typeof n.performance.now?n.performance.now():Date.now()-t};if("mozRequestAnimationFrame"in n?e="moz":"webkitRequestAnimationFrame"in n&&(e="webkit"),e)n.requestAnimationFrame=function(t){return n[e+"RequestAnimationFrame"](function(){t(o())})},n.cancelAnimationFrame=n[e+"CancelAnimationFrame"];else{var i=Date.now();n.requestAnimationFrame=function(n){if("function"!=typeof n)throw new TypeError(n+" is not a function");var e=Date.now(),t=16+i-e;return t<0&&(t=0),i=e,setTimeout(function(){i=Date.now(),n(o())},t)},n.cancelAnimationFrame=function(n){clearTimeout(n)}}}(self);}if (!("function"==typeof document.head.animate&&function(){try{return!!document.createElement("DIV").animate({opacity:[0,1]},{direction:"alternate",duration:1,iterations:1})}catch(t){return!1}}()
)) {!function(){var t={},e={};!function(t,e){function n(t){if("number"==typeof t)return t;var e={};for(var n in t)e[n]=t[n];return e}function r(){this._delay=0,this._endDelay=0,this._fill="none",this._iterationStart=0,this._iterations=1,this._duration=0,this._playbackRate=1,this._direction="normal",this._easing="linear",this._easingFunction=w}function i(){return t.isDeprecated("Invalid timing inputs","2016-03-02","TypeError exceptions will be thrown instead.",!0)}function o(e,n,i){var o=new r;return n&&(o.fill="both",o.duration="auto"),"number"!=typeof e||isNaN(e)?void 0!==e&&Object.getOwnPropertyNames(e).forEach(function(n){if("auto"!=e[n]){if(("number"==typeof o[n]||"duration"==n)&&("number"!=typeof e[n]||isNaN(e[n])))return;if("fill"==n&&-1==T.indexOf(e[n]))return;if("direction"==n&&-1==x.indexOf(e[n]))return;if("playbackRate"==n&&1!==e[n]&&t.isDeprecated("AnimationEffectTiming.playbackRate","2014-11-28","Use Animation.playbackRate instead."))return;o[n]=e[n]}}):o.duration=e,o}function a(t){return"number"==typeof t&&(t=isNaN(t)?{duration:0}:{duration:t}),t}function s(e,n){return e=t.numericTimingToObject(e),o(e,n)}function u(t,e,n,r){return t<0||t>1||n<0||n>1?w:function(i){function o(t,e,n){return 3*t*(1-n)*(1-n)*n+3*e*(1-n)*n*n+n*n*n}if(i<=0){var a=0;return t>0?a=e/t:!e&&n>0&&(a=r/n),a*i}if(i>=1){var s=0;return n<1?s=(r-1)/(n-1):1==n&&t<1&&(s=(e-1)/(t-1)),1+s*(i-1)}for(var u=0,c=1;u<c;){var l=(u+c)/2,f=o(t,n,l);if(Math.abs(i-f)<1e-5)return o(e,r,l);f<i?u=l:c=l}return o(e,r,l)}}function c(t,e){return function(n){if(n>=1)return 1;var r=1/t;return(n+=e*r)-n%r}}function l(t){R||(R=document.createElement("div").style),R.animationTimingFunction="",R.animationTimingFunction=t;var e=R.animationTimingFunction;if(""==e&&i())throw new TypeError(t+" is not a valid value for easing");return e}function f(t){if("linear"==t)return w;var e=E.exec(t);if(e)return u.apply(this,e.slice(1).map(Number));var n=O.exec(t);if(n)return c(Number(n[1]),S);var r=j.exec(t);return r?c(Number(r[1]),{start:N,middle:k,end:S}[r[2]]):P[t]||w}function d(t){return Math.abs(h(t)/t.playbackRate)}function h(t){return 0===t.duration||0===t.iterations?0:t.duration*t.iterations}function p(t,e,n){if(null==e)return M;var r=n.delay+t+n.endDelay;return e<Math.min(n.delay,r)?D:e>=Math.min(n.delay+t,r)?L:C}function m(t,e,n,r,i){switch(r){case D:return"backwards"==e||"both"==e?0:null;case C:return n-i;case L:return"forwards"==e||"both"==e?t:null;case M:return null}}function g(t,e,n,r,i){var o=i;return 0===t?e!==D&&(o+=n):o+=r/t,o}function v(t,e,n,r,i,o){var a=t===1/0?e%1:t%1;return 0!==a||n!==L||0===r||0===i&&0!==o||(a=1),a}function b(t,e,n,r){return t===L&&e===1/0?1/0:1===n?Math.floor(r)-1:Math.floor(r)}function _(t,e,n){var r=t;if("normal"!==t&&"reverse"!==t){var i=e;"alternate-reverse"===t&&(i+=1),r="normal",i!==1/0&&i%2!=0&&(r="reverse")}return"normal"===r?n:1-n}function y(t,e,n){var r=p(t,e,n),i=m(t,n.fill,e,r,n.delay);if(null===i)return null;var o=g(n.duration,r,n.iterations,i,n.iterationStart),a=v(o,n.iterationStart,r,n.iterations,i,n.duration),s=b(r,n.iterations,a,o),u=_(n.direction,s,a);return n._easingFunction(u)}var T="backwards|forwards|both|none".split("|"),x="reverse|alternate|alternate-reverse".split("|"),w=function(t){return t};r.prototype={_setMember:function(e,n){this["_"+e]=n,this._effect&&(this._effect._timingInput[e]=n,this._effect._timing=t.normalizeTimingInput(this._effect._timingInput),this._effect.activeDuration=t.calculateActiveDuration(this._effect._timing),this._effect._animation&&this._effect._animation._rebuildUnderlyingAnimation())},get playbackRate(){return this._playbackRate},set delay(t){this._setMember("delay",t)},get delay(){return this._delay},set endDelay(t){this._setMember("endDelay",t)},get endDelay(){return this._endDelay},set fill(t){this._setMember("fill",t)},get fill(){return this._fill},set iterationStart(t){if((isNaN(t)||t<0)&&i())throw new TypeError("iterationStart must be a non-negative number, received: "+t);this._setMember("iterationStart",t)},get iterationStart(){return this._iterationStart},set duration(t){if("auto"!=t&&(isNaN(t)||t<0)&&i())throw new TypeError("duration must be non-negative or auto, received: "+t);this._setMember("duration",t)},get duration(){return this._duration},set direction(t){this._setMember("direction",t)},get direction(){return this._direction},set easing(t){this._easingFunction=f(l(t)),this._setMember("easing",t)},get easing(){return this._easing},set iterations(t){if((isNaN(t)||t<0)&&i())throw new TypeError("iterations must be non-negative, received: "+t);this._setMember("iterations",t)},get iterations(){return this._iterations}};var N=1,k=.5,S=0,P={ease:u(.25,.1,.25,1),"ease-in":u(.42,0,1,1),"ease-out":u(0,0,.58,1),"ease-in-out":u(.42,0,.58,1),"step-start":c(1,N),"step-middle":c(1,k),"step-end":c(1,S)},R=null,A="\\s*(-?\\d+\\.?\\d*|-?\\.\\d+)\\s*",E=new RegExp("cubic-bezier\\("+A+","+A+","+A+","+A+"\\)"),O=/steps\(\s*(\d+)\s*\)/,j=/steps\(\s*(\d+)\s*,\s*(start|middle|end)\s*\)/,M=0,D=1,L=2,C=3;t.cloneTimingInput=n,t.makeTiming=o,t.numericTimingToObject=a,t.normalizeTimingInput=s,t.calculateActiveDuration=d,t.calculateIterationProgress=y,t.calculatePhase=p,t.normalizeEasing=l,t.parseEasingFunction=f}(t),function(t,e){function n(t,e){return t in l?l[t][e]||e:e}function r(t){return"display"===t||0===t.lastIndexOf("animation",0)||0===t.lastIndexOf("transition",0)}function i(t,e,i){if(!r(t)){var o=s[t];if(o){u.style[t]=e;for(var a in o){var c=o[a],l=u.style[c];i[c]=n(c,l)}}else i[t]=n(t,e)}}function o(t){var e=[];for(var n in t)if(!(n in["easing","offset","composite"])){var r=t[n];Array.isArray(r)||(r=[r]);for(var i,o=r.length,a=0;a<o;a++)i={},i.offset="offset"in t?t.offset:1==o?1:a/(o-1),"easing"in t&&(i.easing=t.easing),"composite"in t&&(i.composite=t.composite),i[n]=r[a],e.push(i)}return e.sort(function(t,e){return t.offset-e.offset}),e}function a(e){if(null==e)return[];window.Symbol&&Symbol.iterator&&Array.prototype.from&&e[Symbol.iterator]&&(e=Array.from(e)),Array.isArray(e)||(e=o(e));for(var n=e.map(function(e){var n={};for(var r in e){var o=e[r];if("offset"==r){if(null!=o){if(o=Number(o),!isFinite(o))throw new TypeError("Keyframe offsets must be numbers.");if(o<0||o>1)throw new TypeError("Keyframe offsets must be between 0 and 1.")}}else if("composite"==r){if("add"==o||"accumulate"==o)throw{type:DOMException.NOT_SUPPORTED_ERR,name:"NotSupportedError",message:"add compositing is not supported"};if("replace"!=o)throw new TypeError("Invalid composite mode "+o+".")}else o="easing"==r?t.normalizeEasing(o):""+o;i(r,o,n)}return void 0==n.offset&&(n.offset=null),void 0==n.easing&&(n.easing="linear"),n}),r=!0,a=-1/0,s=0;s<n.length;s++){var u=n[s].offset;if(null!=u){if(u<a)throw new TypeError("Keyframes are not loosely sorted by offset. Sort or specify offsets.");a=u}else r=!1}return n=n.filter(function(t){return t.offset>=0&&t.offset<=1}),r||function c(){var t=n.length;null==n[t-1].offset&&(n[t-1].offset=1),t>1&&null==n[0].offset&&(n[0].offset=0);for(var e=0,r=n[0].offset,i=1;i<t;i++){var o=n[i].offset;if(null!=o){for(var a=1;a<i-e;a++)n[e+a].offset=r+(o-r)*a/(i-e);e=i,r=o}}}(),n}var s={background:["backgroundImage","backgroundPosition","backgroundSize","backgroundRepeat","backgroundAttachment","backgroundOrigin","backgroundClip","backgroundColor"],border:["borderTopColor","borderTopStyle","borderTopWidth","borderRightColor","borderRightStyle","borderRightWidth","borderBottomColor","borderBottomStyle","borderBottomWidth","borderLeftColor","borderLeftStyle","borderLeftWidth"],borderBottom:["borderBottomWidth","borderBottomStyle","borderBottomColor"],borderColor:["borderTopColor","borderRightColor","borderBottomColor","borderLeftColor"],borderLeft:["borderLeftWidth","borderLeftStyle","borderLeftColor"],borderRadius:["borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"],borderRight:["borderRightWidth","borderRightStyle","borderRightColor"],borderTop:["borderTopWidth","borderTopStyle","borderTopColor"],borderWidth:["borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth"],flex:["flexGrow","flexShrink","flexBasis"],font:["fontFamily","fontSize","fontStyle","fontVariant","fontWeight","lineHeight"],margin:["marginTop","marginRight","marginBottom","marginLeft"],outline:["outlineColor","outlineStyle","outlineWidth"],padding:["paddingTop","paddingRight","paddingBottom","paddingLeft"]},u=document.createElementNS("http://www.w3.org/1999/xhtml","div"),c={thin:"1px",medium:"3px",thick:"5px"},l={borderBottomWidth:c,borderLeftWidth:c,borderRightWidth:c,borderTopWidth:c,fontSize:{"xx-small":"60%","x-small":"75%",small:"89%",medium:"100%",large:"120%","x-large":"150%","xx-large":"200%"},fontWeight:{normal:"400",bold:"700"},outlineWidth:c,textShadow:{none:"0px 0px 0px transparent"},boxShadow:{none:"0px 0px 0px 0px transparent"}};t.convertToArrayForm=o,t.normalizeKeyframes=a}(t),function(t){var e={};t.isDeprecated=function(t,n,r,i){var o=i?"are":"is",a=new Date,s=new Date(n);return s.setMonth(s.getMonth()+3),!(a<s&&(t in e||console.warn("Web Animations: "+t+" "+o+" deprecated and will stop working on "+s.toDateString()+". "+r),e[t]=!0,1))},t.deprecated=function(e,n,r,i){var o=i?"are":"is";if(t.isDeprecated(e,n,r,i))throw new Error(e+" "+o+" no longer supported. "+r)}}(t),function(){if(document.documentElement.animate){var n=document.documentElement.animate([],0),r=!0;if(n&&(r=!1,"play|currentTime|pause|reverse|playbackRate|cancel|finish|startTime|playState".split("|").forEach(function(t){void 0===n[t]&&(r=!0)})),!r)return}!function(t,e,n){function r(t){for(var e={},n=0;n<t.length;n++)for(var r in t[n])if("offset"!=r&&"easing"!=r&&"composite"!=r){var i={offset:t[n].offset,easing:t[n].easing,value:t[n][r]};e[r]=e[r]||[],e[r].push(i)}for(var o in e){var a=e[o];if(0!=a[0].offset||1!=a[a.length-1].offset)throw{type:DOMException.NOT_SUPPORTED_ERR,name:"NotSupportedError",message:"Partial keyframes are not supported"}}return e}function i(n){var r=[];for(var i in n)for(var o=n[i],a=0;a<o.length-1;a++){var s=a,u=a+1,c=o[s].offset,l=o[u].offset,f=c,d=l;0==a&&(f=-1/0,0==l&&(u=s)),a==o.length-2&&(d=1/0,1==c&&(s=u)),r.push({applyFrom:f,applyTo:d,startOffset:o[s].offset,endOffset:o[u].offset,easingFunction:t.parseEasingFunction(o[s].easing),property:i,interpolation:e.propertyInterpolation(i,o[s].value,o[u].value)})}return r.sort(function(t,e){return t.startOffset-e.startOffset}),r}e.convertEffectInput=function(n){var o=t.normalizeKeyframes(n),a=r(o),s=i(a);return function(t,n){if(null!=n)s.filter(function(t){return n>=t.applyFrom&&n<t.applyTo}).forEach(function(r){var i=n-r.startOffset,o=r.endOffset-r.startOffset,a=0==o?0:r.easingFunction(i/o);e.apply(t,r.property,r.interpolation(a))});else for(var r in a)"offset"!=r&&"easing"!=r&&"composite"!=r&&e.clear(t,r)}}}(t,e),function(t,e,n){function r(t){return t.replace(/-(.)/g,function(t,e){return e.toUpperCase()})}function i(t,e,n){s[n]=s[n]||[],s[n].push([t,e])}function o(t,e,n){for(var o=0;o<n.length;o++)i(t,e,r(n[o]))}function a(n,i,o){var a=n;/-/.test(n)&&!t.isDeprecated("Hyphenated property names","2016-03-22","Use camelCase instead.",!0)&&(a=r(n)),"initial"!=i&&"initial"!=o||("initial"==i&&(i=u[a]),"initial"==o&&(o=u[a]));for(var c=i==o?[]:s[a],l=0;c&&l<c.length;l++){var f=c[l][0](i),d=c[l][0](o);if(void 0!==f&&void 0!==d){var h=c[l][1](f,d);if(h){var p=e.Interpolation.apply(null,h);return function(t){return 0==t?i:1==t?o:p(t)}}}}return e.Interpolation(!1,!0,function(t){return t?o:i})}var s={};e.addPropertiesHandler=o;var u={backgroundColor:"transparent",backgroundPosition:"0% 0%",borderBottomColor:"currentColor",borderBottomLeftRadius:"0px",borderBottomRightRadius:"0px",borderBottomWidth:"3px",borderLeftColor:"currentColor",borderLeftWidth:"3px",borderRightColor:"currentColor",borderRightWidth:"3px",borderSpacing:"2px",borderTopColor:"currentColor",borderTopLeftRadius:"0px",borderTopRightRadius:"0px",borderTopWidth:"3px",bottom:"auto",clip:"rect(0px, 0px, 0px, 0px)",color:"black",fontSize:"100%",fontWeight:"400",height:"auto",left:"auto",letterSpacing:"normal",lineHeight:"120%",marginBottom:"0px",marginLeft:"0px",marginRight:"0px",marginTop:"0px",maxHeight:"none",maxWidth:"none",minHeight:"0px",minWidth:"0px",opacity:"1.0",outlineColor:"invert",outlineOffset:"0px",outlineWidth:"3px",paddingBottom:"0px",paddingLeft:"0px",paddingRight:"0px",paddingTop:"0px",right:"auto",strokeDasharray:"none",strokeDashoffset:"0px",textIndent:"0px",textShadow:"0px 0px 0px transparent",top:"auto",transform:"",verticalAlign:"0px",visibility:"visible",width:"auto",wordSpacing:"normal",zIndex:"auto"};e.propertyInterpolation=a}(t,e),function(t,e,n){function r(e){var n=t.calculateActiveDuration(e),r=function(r){return t.calculateIterationProgress(n,r,e)};return r._totalDuration=e.delay+n+e.endDelay,r}e.KeyframeEffect=function(n,i,o,a){var s,u=r(t.normalizeTimingInput(o)),c=e.convertEffectInput(i),l=function(){c(n,s)};return l._update=function(t){return null!==(s=u(t))},l._clear=function(){c(n,null)},l._hasSameTarget=function(t){return n===t},l._target=n,l._totalDuration=u._totalDuration,l._id=a,l}}(t,e),function(t,e){function n(t,e){return!(!e.namespaceURI||-1==e.namespaceURI.indexOf("/svg"))&&(a in t||(t[a]=/Trident|MSIE|IEMobile|Edge|Android 4/i.test(t.navigator.userAgent)),t[a])}function r(t,e,n){n.enumerable=!0,n.configurable=!0,Object.defineProperty(t,e,n)}function i(t){this._element=t,this._surrogateStyle=document.createElementNS("http://www.w3.org/1999/xhtml","div").style,this._style=t.style,this._length=0,this._isAnimatedProperty={},this._updateSvgTransformAttr=n(window,t),this._savedTransformAttr=null;for(var e=0;e<this._style.length;e++){var r=this._style[e];this._surrogateStyle[r]=this._style[r]}this._updateIndices()}function o(t){if(!t._webAnimationsPatchedStyle){var e=new i(t);try{r(t,"style",{get:function(){return e}})}catch(e){t.style._set=function(e,n){t.style[e]=n},t.style._clear=function(e){t.style[e]=""}}t._webAnimationsPatchedStyle=t.style}}var a="_webAnimationsUpdateSvgTransformAttr",s={cssText:1,length:1,parentRule:1},u={getPropertyCSSValue:1,getPropertyPriority:1,getPropertyValue:1,item:1,removeProperty:1,setProperty:1},c={removeProperty:1,setProperty:1};i.prototype={get cssText(){return this._surrogateStyle.cssText},set cssText(t){for(var e={},n=0;n<this._surrogateStyle.length;n++)e[this._surrogateStyle[n]]=!0;this._surrogateStyle.cssText=t,this._updateIndices();for(var n=0;n<this._surrogateStyle.length;n++)e[this._surrogateStyle[n]]=!0;for(var r in e)this._isAnimatedProperty[r]||this._style.setProperty(r,this._surrogateStyle.getPropertyValue(r))},get length(){return this._surrogateStyle.length},get parentRule(){return this._style.parentRule},_updateIndices:function(){for(;this._length<this._surrogateStyle.length;)Object.defineProperty(this,this._length,{configurable:!0,enumerable:!1,get:function(t){return function(){return this._surrogateStyle[t]}}(this._length)}),this._length++;for(;this._length>this._surrogateStyle.length;)this._length--,Object.defineProperty(this,this._length,{configurable:!0,enumerable:!1,value:void 0})},_set:function(e,n){this._style[e]=n,this._isAnimatedProperty[e]=!0,this._updateSvgTransformAttr&&"transform"==t.unprefixedPropertyName(e)&&(null==this._savedTransformAttr&&(this._savedTransformAttr=this._element.getAttribute("transform")),this._element.setAttribute("transform",t.transformToSvgMatrix(n)))},_clear:function(e){this._style[e]=this._surrogateStyle[e],this._updateSvgTransformAttr&&"transform"==t.unprefixedPropertyName(e)&&(this._savedTransformAttr?this._element.setAttribute("transform",this._savedTransformAttr):this._element.removeAttribute("transform"),this._savedTransformAttr=null),delete this._isAnimatedProperty[e]}};for(var l in u)i.prototype[l]=function(t,e){return function(){var n=this._surrogateStyle[t].apply(this._surrogateStyle,arguments);return e&&(this._isAnimatedProperty[arguments[0]]||this._style[t].apply(this._style,arguments),this._updateIndices()),n}}(l,l in c);for(var f in document.documentElement.style)f in s||f in u||function(t){r(i.prototype,t,{get:function(){return this._surrogateStyle[t]},set:function(e){this._surrogateStyle[t]=e,this._updateIndices(),this._isAnimatedProperty[t]||(this._style[t]=e)}})}(f);t.apply=function(e,n,r){o(e),e.style._set(t.propertyName(n),r)},t.clear=function(e,n){e._webAnimationsPatchedStyle&&e.style._clear(t.propertyName(n))}}(e),function(t){window.Element.prototype.animate=function(e,n){var r="";return n&&n.id&&(r=n.id),t.timeline._play(t.KeyframeEffect(this,e,n,r))}}(e),function(t,e){function n(t,e,r){if("number"==typeof t&&"number"==typeof e)return t*(1-r)+e*r;if("boolean"==typeof t&&"boolean"==typeof e)return r<.5?t:e;if(t.length==e.length){for(var i=[],o=0;o<t.length;o++)i.push(n(t[o],e[o],r));return i}throw"Mismatched interpolation arguments "+t+":"+e}t.Interpolation=function(t,e,r){return function(i){return r(n(t,e,i))}}}(e),function(t,e){function n(t,e,n){return Math.max(Math.min(t,n),e)}function r(e,r,i){var o=t.dot(e,r);o=n(o,-1,1);var a=[];if(1===o)a=e;else for(var s=Math.acos(o),u=1*Math.sin(i*s)/Math.sqrt(1-o*o),c=0;c<4;c++)a.push(e[c]*(Math.cos(i*s)-o*u)+r[c]*u);return a}var i=function(){function t(t,e){for(var n=[[0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,0,0]],r=0;r<4;r++)for(var i=0;i<4;i++)for(var o=0;o<4;o++)n[r][i]+=e[r][o]*t[o][i];return n}function e(t){return 0==t[0][2]&&0==t[0][3]&&0==t[1][2]&&0==t[1][3]&&0==t[2][0]&&0==t[2][1]&&1==t[2][2]&&0==t[2][3]&&0==t[3][2]&&1==t[3][3]}function n(n,r,i,o,a){for(var s=[[1,0,0,0],[0,1,0,0],[0,0,1,0],[0,0,0,1]],u=0;u<4;u++)s[u][3]=a[u];for(var u=0;u<3;u++)for(var c=0;c<3;c++)s[3][u]+=n[c]*s[c][u];var l=o[0],f=o[1],d=o[2],h=o[3],p=[[1,0,0,0],[0,1,0,0],[0,0,1,0],[0,0,0,1]];p[0][0]=1-2*(f*f+d*d),p[0][1]=2*(l*f-d*h),p[0][2]=2*(l*d+f*h),p[1][0]=2*(l*f+d*h),p[1][1]=1-2*(l*l+d*d),p[1][2]=2*(f*d-l*h),p[2][0]=2*(l*d-f*h),p[2][1]=2*(f*d+l*h),p[2][2]=1-2*(l*l+f*f),s=t(s,p);var m=[[1,0,0,0],[0,1,0,0],[0,0,1,0],[0,0,0,1]];i[2]&&(m[2][1]=i[2],s=t(s,m)),i[1]&&(m[2][1]=0,m[2][0]=i[0],s=t(s,m)),i[0]&&(m[2][0]=0,m[1][0]=i[0],s=t(s,m));for(var u=0;u<3;u++)for(var c=0;c<3;c++)s[u][c]*=r[u];return e(s)?[s[0][0],s[0][1],s[1][0],s[1][1],s[3][0],s[3][1]]:s[0].concat(s[1],s[2],s[3])}return n}();t.composeMatrix=i,t.quat=r}(e),function(t,e,n){t.sequenceNumber=0;var r=function(t,e,n){this.target=t,this.currentTime=e,this.timelineTime=n,this.type="finish",this.bubbles=!1,this.cancelable=!1,this.currentTarget=t,this.defaultPrevented=!1,this.eventPhase=Event.AT_TARGET,this.timeStamp=Date.now()};e.Animation=function(e){this.id="",e&&e._id&&(this.id=e._id),this._sequenceNumber=t.sequenceNumber++,this._currentTime=0,this._startTime=null,this._paused=!1,this._playbackRate=1,this._inTimeline=!0,this._finishedFlag=!0,this.onfinish=null,this._finishHandlers=[],this._effect=e,this._inEffect=this._effect._update(0),this._idle=!0,this._currentTimePending=!1},e.Animation.prototype={_ensureAlive:function(){this.playbackRate<0&&0===this.currentTime?this._inEffect=this._effect._update(-1):this._inEffect=this._effect._update(this.currentTime),this._inTimeline||!this._inEffect&&this._finishedFlag||(this._inTimeline=!0,e.timeline._animations.push(this))},_tickCurrentTime:function(t,e){t!=this._currentTime&&(this._currentTime=t,this._isFinished&&!e&&(this._currentTime=this._playbackRate>0?this._totalDuration:0),this._ensureAlive())},get currentTime(){return this._idle||this._currentTimePending?null:this._currentTime},set currentTime(t){t=+t,isNaN(t)||(e.restart(),this._paused||null==this._startTime||(this._startTime=this._timeline.currentTime-t/this._playbackRate),this._currentTimePending=!1,this._currentTime!=t&&(this._idle&&(this._idle=!1,this._paused=!0),this._tickCurrentTime(t,!0),e.applyDirtiedAnimation(this)))},get startTime(){return this._startTime},set startTime(t){t=+t,isNaN(t)||this._paused||this._idle||(this._startTime=t,this._tickCurrentTime((this._timeline.currentTime-this._startTime)*this.playbackRate),e.applyDirtiedAnimation(this))},get playbackRate(){return this._playbackRate},set playbackRate(t){if(t!=this._playbackRate){var n=this.currentTime;this._playbackRate=t,this._startTime=null,"paused"!=this.playState&&"idle"!=this.playState&&(this._finishedFlag=!1,this._idle=!1,this._ensureAlive(),e.applyDirtiedAnimation(this)),null!=n&&(this.currentTime=n)}},get _isFinished(){return!this._idle&&(this._playbackRate>0&&this._currentTime>=this._totalDuration||this._playbackRate<0&&this._currentTime<=0)},get _totalDuration(){return this._effect._totalDuration},get playState(){return this._idle?"idle":null==this._startTime&&!this._paused&&0!=this.playbackRate||this._currentTimePending?"pending":this._paused?"paused":this._isFinished?"finished":"running"},_rewind:function(){if(this._playbackRate>=0)this._currentTime=0;else{if(!(this._totalDuration<1/0))throw new DOMException("Unable to rewind negative playback rate animation with infinite duration","InvalidStateError");this._currentTime=this._totalDuration}},play:function(){this._paused=!1,(this._isFinished||this._idle)&&(this._rewind(),this._startTime=null),this._finishedFlag=!1,this._idle=!1,this._ensureAlive(),e.applyDirtiedAnimation(this)},pause:function(){this._isFinished||this._paused||this._idle?this._idle&&(this._rewind(),this._idle=!1):this._currentTimePending=!0,this._startTime=null,this._paused=!0},finish:function(){this._idle||(this.currentTime=this._playbackRate>0?this._totalDuration:0,this._startTime=this._totalDuration-this.currentTime,this._currentTimePending=!1,e.applyDirtiedAnimation(this))},cancel:function(){this._inEffect&&(this._inEffect=!1,this._idle=!0,this._paused=!1,this._finishedFlag=!0,this._currentTime=0,this._startTime=null,this._effect._update(null),e.applyDirtiedAnimation(this))},reverse:function(){this.playbackRate*=-1,this.play()},addEventListener:function(t,e){"function"==typeof e&&"finish"==t&&this._finishHandlers.push(e)},removeEventListener:function(t,e){if("finish"==t){var n=this._finishHandlers.indexOf(e);n>=0&&this._finishHandlers.splice(n,1)}},_fireEvents:function(t){if(this._isFinished){if(!this._finishedFlag){var e=new r(this,this._currentTime,t),n=this._finishHandlers.concat(this.onfinish?[this.onfinish]:[]);setTimeout(function(){n.forEach(function(t){t.call(e.target,e)})},0),this._finishedFlag=!0}}else this._finishedFlag=!1},_tick:function(t,e){this._idle||this._paused||(null==this._startTime?e&&(this.startTime=t-this._currentTime/this.playbackRate):this._isFinished||this._tickCurrentTime((t-this._startTime)*this.playbackRate)),e&&(this._currentTimePending=!1,this._fireEvents(t))},get _needsTick(){return this.playState in{pending:1,running:1}||!this._finishedFlag},_targetAnimations:function(){var t=this._effect._target;return t._activeAnimations||(t._activeAnimations=[]),t._activeAnimations},_markTarget:function(){var t=this._targetAnimations();-1===t.indexOf(this)&&t.push(this)},_unmarkTarget:function(){var t=this._targetAnimations(),e=t.indexOf(this);-1!==e&&t.splice(e,1)}}}(t,e),function(t,e,n){function r(t){var e=c;c=[],t<g.currentTime&&(t=g.currentTime),g._animations.sort(i),g._animations=s(t,!0,g._animations)[0],e.forEach(function(e){e[1](t)}),a(),f=void 0}function i(t,e){return t._sequenceNumber-e._sequenceNumber}function o(){this._animations=[],this.currentTime=window.performance&&performance.now?performance.now():0}function a(){p.forEach(function(t){t()}),p.length=0}function s(t,n,r){m=!0,h=!1,e.timeline.currentTime=t,d=!1;var i=[],o=[],a=[],s=[];return r.forEach(function(e){e._tick(t,n),e._inEffect?(o.push(e._effect),e._markTarget()):(i.push(e._effect),e._unmarkTarget()),e._needsTick&&(d=!0);var r=e._inEffect||e._needsTick;e._inTimeline=r,r?a.push(e):s.push(e)}),p.push.apply(p,i),p.push.apply(p,o),d&&requestAnimationFrame(function(){}),m=!1,[a,s]}var u=window.requestAnimationFrame,c=[],l=0;window.requestAnimationFrame=function(t){var e=l++;return 0==c.length&&u(r),c.push([e,t]),e},window.cancelAnimationFrame=function(t){c.forEach(function(e){e[0]==t&&(e[1]=function(){})})},o.prototype={_play:function(n){n._timing=t.normalizeTimingInput(n.timing);var r=new e.Animation(n);return r._idle=!1,r._timeline=this,this._animations.push(r),e.restart(),e.applyDirtiedAnimation(r),r}};var f=void 0,d=!1,h=!1;e.restart=function(){return d||(d=!0,requestAnimationFrame(function(){}),h=!0),h},e.applyDirtiedAnimation=function(t){if(!m){t._markTarget();var n=t._targetAnimations();n.sort(i),s(e.timeline.currentTime,!1,n.slice())[1].forEach(function(t){var e=g._animations.indexOf(t);-1!==e&&g._animations.splice(e,1)}),a()}};var p=[],m=!1,g=new o;e.timeline=g}(t,e),function(t,e){function n(t,e){for(var n=0,r=0;r<t.length;r++)n+=t[r]*e[r];return n}function r(t,e){return[t[0]*e[0]+t[4]*e[1]+t[8]*e[2]+t[12]*e[3],t[1]*e[0]+t[5]*e[1]+t[9]*e[2]+t[13]*e[3],t[2]*e[0]+t[6]*e[1]+t[10]*e[2]+t[14]*e[3],t[3]*e[0]+t[7]*e[1]+t[11]*e[2]+t[15]*e[3],t[0]*e[4]+t[4]*e[5]+t[8]*e[6]+t[12]*e[7],t[1]*e[4]+t[5]*e[5]+t[9]*e[6]+t[13]*e[7],t[2]*e[4]+t[6]*e[5]+t[10]*e[6]+t[14]*e[7],t[3]*e[4]+t[7]*e[5]+t[11]*e[6]+t[15]*e[7],t[0]*e[8]+t[4]*e[9]+t[8]*e[10]+t[12]*e[11],t[1]*e[8]+t[5]*e[9]+t[9]*e[10]+t[13]*e[11],t[2]*e[8]+t[6]*e[9]+t[10]*e[10]+t[14]*e[11],t[3]*e[8]+t[7]*e[9]+t[11]*e[10]+t[15]*e[11],t[0]*e[12]+t[4]*e[13]+t[8]*e[14]+t[12]*e[15],t[1]*e[12]+t[5]*e[13]+t[9]*e[14]+t[13]*e[15],t[2]*e[12]+t[6]*e[13]+t[10]*e[14]+t[14]*e[15],t[3]*e[12]+t[7]*e[13]+t[11]*e[14]+t[15]*e[15]]}function i(t){var e=t.rad||0;return((t.deg||0)/360+(t.grad||0)/400+(t.turn||0))*(2*Math.PI)+e}function o(t){switch(t.t){case"rotatex":var e=i(t.d[0]);return[1,0,0,0,0,Math.cos(e),Math.sin(e),0,0,-Math.sin(e),Math.cos(e),0,0,0,0,1];case"rotatey":var e=i(t.d[0]);return[Math.cos(e),0,-Math.sin(e),0,0,1,0,0,Math.sin(e),0,Math.cos(e),0,0,0,0,1];case"rotate":case"rotatez":var e=i(t.d[0]);return[Math.cos(e),Math.sin(e),0,0,-Math.sin(e),Math.cos(e),0,0,0,0,1,0,0,0,0,1];case"rotate3d":var n=t.d[0],r=t.d[1],o=t.d[2],e=i(t.d[3]),a=n*n+r*r+o*o;if(0===a)n=1,r=0,o=0;else if(1!==a){var s=Math.sqrt(a);n/=s,r/=s,o/=s}var u=Math.sin(e/2),c=u*Math.cos(e/2),l=u*u;return[1-2*(r*r+o*o)*l,2*(n*r*l+o*c),2*(n*o*l-r*c),0,2*(n*r*l-o*c),1-2*(n*n+o*o)*l,2*(r*o*l+n*c),0,2*(n*o*l+r*c),2*(r*o*l-n*c),1-2*(n*n+r*r)*l,0,0,0,0,1];case"scale":return[t.d[0],0,0,0,0,t.d[1],0,0,0,0,1,0,0,0,0,1];case"scalex":return[t.d[0],0,0,0,0,1,0,0,0,0,1,0,0,0,0,1];case"scaley":return[1,0,0,0,0,t.d[0],0,0,0,0,1,0,0,0,0,1];case"scalez":return[1,0,0,0,0,1,0,0,0,0,t.d[0],0,0,0,0,1];case"scale3d":return[t.d[0],0,0,0,0,t.d[1],0,0,0,0,t.d[2],0,0,0,0,1];case"skew":var f=i(t.d[0]),d=i(t.d[1]);return[1,Math.tan(d),0,0,Math.tan(f),1,0,0,0,0,1,0,0,0,0,1];case"skewx":var e=i(t.d[0]);return[1,0,0,0,Math.tan(e),1,0,0,0,0,1,0,0,0,0,1];case"skewy":var e=i(t.d[0]);return[1,Math.tan(e),0,0,0,1,0,0,0,0,1,0,0,0,0,1];case"translate":var n=t.d[0].px||0,r=t.d[1].px||0;return[1,0,0,0,0,1,0,0,0,0,1,0,n,r,0,1];case"translatex":var n=t.d[0].px||0;return[1,0,0,0,0,1,0,0,0,0,1,0,n,0,0,1];case"translatey":var r=t.d[0].px||0;return[1,0,0,0,0,1,0,0,0,0,1,0,0,r,0,1];case"translatez":var o=t.d[0].px||0;return[1,0,0,0,0,1,0,0,0,0,1,0,0,0,o,1];case"translate3d":var n=t.d[0].px||0,r=t.d[1].px||0,o=t.d[2].px||0;return[1,0,0,0,0,1,0,0,0,0,1,0,n,r,o,1];case"perspective":return[1,0,0,0,0,1,0,0,0,0,1,t.d[0].px?-1/t.d[0].px:0,0,0,0,1];case"matrix":return[t.d[0],t.d[1],0,0,t.d[2],t.d[3],0,0,0,0,1,0,t.d[4],t.d[5],0,1];case"matrix3d":return t.d}}function a(t){return 0===t.length?[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]:t.map(o).reduce(r)}function s(t){return[u(a(t))]}var u=function(){function t(t){return t[0][0]*t[1][1]*t[2][2]+t[1][0]*t[2][1]*t[0][2]+t[2][0]*t[0][1]*t[1][2]-t[0][2]*t[1][1]*t[2][0]-t[1][2]*t[2][1]*t[0][0]-t[2][2]*t[0][1]*t[1][0]}function e(e){for(var n=1/t(e),r=e[0][0],i=e[0][1],o=e[0][2],a=e[1][0],s=e[1][1],u=e[1][2],c=e[2][0],l=e[2][1],f=e[2][2],d=[[(s*f-u*l)*n,(o*l-i*f)*n,(i*u-o*s)*n,0],[(u*c-a*f)*n,(r*f-o*c)*n,(o*a-r*u)*n,0],[(a*l-s*c)*n,(c*i-r*l)*n,(r*s-i*a)*n,0]],h=[],p=0;p<3;p++){for(var m=0,g=0;g<3;g++)m+=e[3][g]*d[g][p];h.push(m)}return h.push(1),d.push(h),d}function r(t){return[[t[0][0],t[1][0],t[2][0],t[3][0]],[t[0][1],t[1][1],t[2][1],t[3][1]],[t[0][2],t[1][2],t[2][2],t[3][2]],[t[0][3],t[1][3],t[2][3],t[3][3]]]}function i(t,e){for(var n=[],r=0;r<4;r++){for(var i=0,o=0;o<4;o++)i+=t[o]*e[o][r];n.push(i)}return n}function o(t){var e=a(t);return[t[0]/e,t[1]/e,t[2]/e]}function a(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2])}function s(t,e,n,r){return[n*t[0]+r*e[0],n*t[1]+r*e[1],n*t[2]+r*e[2]]}function u(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function c(c){var l=[c.slice(0,4),c.slice(4,8),c.slice(8,12),c.slice(12,16)];if(1!==l[3][3])return null;for(var f=[],d=0;d<4;d++)f.push(l[d].slice());for(var d=0;d<3;d++)f[d][3]=0;if(0===t(f))return null;var h,p=[];l[0][3]||l[1][3]||l[2][3]?(p.push(l[0][3]),p.push(l[1][3]),p.push(l[2][3]),p.push(l[3][3]),h=i(p,r(e(f)))):h=[0,0,0,1];var m=l[3].slice(0,3),g=[];g.push(l[0].slice(0,3));var v=[];v.push(a(g[0])),g[0]=o(g[0]);var b=[];g.push(l[1].slice(0,3)),b.push(n(g[0],g[1])),g[1]=s(g[1],g[0],1,-b[0]),v.push(a(g[1])),g[1]=o(g[1]),b[0]/=v[1],g.push(l[2].slice(0,3)),b.push(n(g[0],g[2])),g[2]=s(g[2],g[0],1,-b[1]),b.push(n(g[1],g[2])),g[2]=s(g[2],g[1],1,-b[2]),v.push(a(g[2])),g[2]=o(g[2]),b[1]/=v[2],b[2]/=v[2];var _=u(g[1],g[2]);if(n(g[0],_)<0)for(var d=0;d<3;d++)v[d]*=-1,g[d][0]*=-1,g[d][1]*=-1,g[d][2]*=-1;var y,T,x=g[0][0]+g[1][1]+g[2][2]+1;return x>1e-4?(y=.5/Math.sqrt(x),T=[(g[2][1]-g[1][2])*y,(g[0][2]-g[2][0])*y,(g[1][0]-g[0][1])*y,.25/y]):g[0][0]>g[1][1]&&g[0][0]>g[2][2]?(y=2*Math.sqrt(1+g[0][0]-g[1][1]-g[2][2]),T=[.25*y,(g[0][1]+g[1][0])/y,(g[0][2]+g[2][0])/y,(g[2][1]-g[1][2])/y]):g[1][1]>g[2][2]?(y=2*Math.sqrt(1+g[1][1]-g[0][0]-g[2][2]),T=[(g[0][1]+g[1][0])/y,.25*y,(g[1][2]+g[2][1])/y,(g[0][2]-g[2][0])/y]):(y=2*Math.sqrt(1+g[2][2]-g[0][0]-g[1][1]),T=[(g[0][2]+g[2][0])/y,(g[1][2]+g[2][1])/y,.25*y,(g[1][0]-g[0][1])/y]),[m,v,b,T,h]}return c}();t.dot=n,t.makeMatrixDecomposition=s,t.transformListToMatrix=a}(e),function(t){function e(t,e){var n=t.exec(e);if(n)return n=t.ignoreCase?n[0].toLowerCase():n[0],[n,e.substr(n.length)]}function n(t,e){e=e.replace(/^\s*/,"");var n=t(e);if(n)return[n[0],n[1].replace(/^\s*/,"")]}function r(t,r,i){t=n.bind(null,t);for(var o=[];;){var a=t(i);if(!a)return[o,i];if(o.push(a[0]),i=a[1],!(a=e(r,i))||""==a[1])return[o,i];i=a[1]}}function i(t,e){for(var n=0,r=0;r<e.length&&(!/\s|,/.test(e[r])||0!=n);r++)if("("==e[r])n++;else if(")"==e[r]&&(n--,0==n&&r++,n<=0))break;var i=t(e.substr(0,r));return void 0==i?void 0:[i,e.substr(r)]}function o(t,e){for(var n=t,r=e;n&&r;)n>r?n%=r:r%=n;return n=t*e/(n+r)}function a(t){return function(e){var n=t(e);return n&&(n[0]=void 0),n}}function s(t,e){return function(n){return t(n)||[e,n]}}function u(e,n){for(var r=[],i=0;i<e.length;i++){var o=t.consumeTrimmed(e[i],n);if(!o||""==o[0])return;void 0!==o[0]&&r.push(o[0]),n=o[1]}if(""==n)return r}function c(t,e,n,r,i){for(var a=[],s=[],u=[],c=o(r.length,i.length),l=0;l<c;l++){var f=e(r[l%r.length],i[l%i.length]);if(!f)return;a.push(f[0]),s.push(f[1]),u.push(f[2])}return[a,s,function(e){var r=e.map(function(t,e){return u[e](t)}).join(n);return t?t(r):r}]}function l(t,e,n){for(var r=[],i=[],o=[],a=0,s=0;s<n.length;s++)if("function"==typeof n[s]){var u=n[s](t[a],e[a++]);r.push(u[0]),i.push(u[1]),o.push(u[2])}else!function(t){r.push(!1),i.push(!1),o.push(function(){return n[t]})}(s);return[r,i,function(t){for(var e="",n=0;n<t.length;n++)e+=o[n](t[n]);return e}]}t.consumeToken=e,t.consumeTrimmed=n,t.consumeRepeated=r,t.consumeParenthesised=i,t.ignore=a,t.optional=s,t.consumeList=u,t.mergeNestedRepeated=c.bind(null,null),t.mergeWrappedNestedRepeated=c,t.mergeList=l}(e),function(t){function e(e){function n(e){var n=t.consumeToken(/^inset/i,e);return n?(r.inset=!0,n):(n=t.consumeLengthOrPercent(e))?(r.lengths.push(n[0]),n):(n=t.consumeColor(e),n?(r.color=n[0],n):void 0)}var r={inset:!1,lengths:[],color:null},i=t.consumeRepeated(n,/^/,e);if(i&&i[0].length)return[r,i[1]]}function n(n){
var r=t.consumeRepeated(e,/^,/,n);if(r&&""==r[1])return r[0]}function r(e,n){for(;e.lengths.length<Math.max(e.lengths.length,n.lengths.length);)e.lengths.push({px:0});for(;n.lengths.length<Math.max(e.lengths.length,n.lengths.length);)n.lengths.push({px:0});if(e.inset==n.inset&&!!e.color==!!n.color){for(var r,i=[],o=[[],0],a=[[],0],s=0;s<e.lengths.length;s++){var u=t.mergeDimensions(e.lengths[s],n.lengths[s],2==s);o[0].push(u[0]),a[0].push(u[1]),i.push(u[2])}if(e.color&&n.color){var c=t.mergeColors(e.color,n.color);o[1]=c[0],a[1]=c[1],r=c[2]}return[o,a,function(t){for(var n=e.inset?"inset ":" ",o=0;o<i.length;o++)n+=i[o](t[0][o])+" ";return r&&(n+=r(t[1])),n}]}}function i(e,n,r,i){function o(t){return{inset:t,color:[0,0,0,0],lengths:[{px:0},{px:0},{px:0},{px:0}]}}for(var a=[],s=[],u=0;u<r.length||u<i.length;u++){var c=r[u]||o(i[u].inset),l=i[u]||o(r[u].inset);a.push(c),s.push(l)}return t.mergeNestedRepeated(e,n,a,s)}var o=i.bind(null,r,", ");t.addPropertiesHandler(n,o,["box-shadow","text-shadow"])}(e),function(t,e){function n(t){return t.toFixed(3).replace(/0+$/,"").replace(/\.$/,"")}function r(t,e,n){return Math.min(e,Math.max(t,n))}function i(t){if(/^\s*[-+]?(\d*\.)?\d+\s*$/.test(t))return Number(t)}function o(t,e){return[t,e,n]}function a(t,e){if(0!=t)return u(0,1/0)(t,e)}function s(t,e){return[t,e,function(t){return Math.round(r(1,1/0,t))}]}function u(t,e){return function(i,o){return[i,o,function(i){return n(r(t,e,i))}]}}function c(t){var e=t.trim().split(/\s*[\s,]\s*/);if(0!==e.length){for(var n=[],r=0;r<e.length;r++){var o=i(e[r]);if(void 0===o)return;n.push(o)}return n}}function l(t,e){if(t.length==e.length)return[t,e,function(t){return t.map(n).join(" ")}]}function f(t,e){return[t,e,Math.round]}t.clamp=r,t.addPropertiesHandler(c,l,["stroke-dasharray"]),t.addPropertiesHandler(i,u(0,1/0),["border-image-width","line-height"]),t.addPropertiesHandler(i,u(0,1),["opacity","shape-image-threshold"]),t.addPropertiesHandler(i,a,["flex-grow","flex-shrink"]),t.addPropertiesHandler(i,s,["orphans","widows"]),t.addPropertiesHandler(i,f,["z-index"]),t.parseNumber=i,t.parseNumberList=c,t.mergeNumbers=o,t.numberToString=n}(e),function(t,e){function n(t,e){if("visible"==t||"visible"==e)return[0,1,function(n){return n<=0?t:n>=1?e:"visible"}]}t.addPropertiesHandler(String,n,["visibility"])}(e),function(t,e){function n(t){t=t.trim(),o.fillStyle="#000",o.fillStyle=t;var e=o.fillStyle;if(o.fillStyle="#fff",o.fillStyle=t,e==o.fillStyle){o.fillRect(0,0,1,1);var n=o.getImageData(0,0,1,1).data;o.clearRect(0,0,1,1);var r=n[3]/255;return[n[0]*r,n[1]*r,n[2]*r,r]}}function r(e,n){return[e,n,function(e){if(e[3])for(var n=0;n<3;n++)e[n]=Math.round(function r(t){return Math.max(0,Math.min(255,t))}(e[n]/e[3]));return e[3]=t.numberToString(t.clamp(0,1,e[3])),"rgba("+e.join(",")+")"}]}var i=document.createElementNS("http://www.w3.org/1999/xhtml","canvas");i.width=i.height=1;var o=i.getContext("2d");t.addPropertiesHandler(n,r,["background-color","border-bottom-color","border-left-color","border-right-color","border-top-color","color","fill","flood-color","lighting-color","outline-color","stop-color","stroke","text-decoration-color"]),t.consumeColor=t.consumeParenthesised.bind(null,n),t.mergeColors=r}(e),function(t,e){function n(t){function e(){var e=s.exec(t);a=e?e[0]:void 0}function n(){var t=Number(a);return e(),t}function r(){if("("!==a)return n();e();var t=o();return")"!==a?NaN:(e(),t)}function i(){for(var t=r();"*"===a||"/"===a;){var n=a;e();var i=r();"*"===n?t*=i:t/=i}return t}function o(){for(var t=i();"+"===a||"-"===a;){var n=a;e();var r=i();"+"===n?t+=r:t-=r}return t}var a,s=/([\+\-\w\.]+|[\(\)\*\/])/g;return e(),o()}function r(t,e){if("0"==(e=e.trim().toLowerCase())&&"px".search(t)>=0)return{px:0};if(/^[^(]*$|^calc/.test(e)){e=e.replace(/calc\(/g,"(");var r={};e=e.replace(t,function(t){return r[t]=null,"U"+t});for(var i="U("+t.source+")",o=e.replace(/[-+]?(\d*\.)?\d+([Ee][-+]?\d+)?/g,"N").replace(new RegExp("N"+i,"g"),"D").replace(/\s[+-]\s/g,"O").replace(/\s/g,""),a=[/N\*(D)/g,/(N|D)[*\/]N/g,/(N|D)O\1/g,/\((N|D)\)/g],s=0;s<a.length;)a[s].test(o)?(o=o.replace(a[s],"$1"),s=0):s++;if("D"==o){for(var u in r){var c=n(e.replace(new RegExp("U"+u,"g"),"").replace(new RegExp(i,"g"),"*0"));if(!isFinite(c))return;r[u]=c}return r}}}function i(t,e){return o(t,e,!0)}function o(e,n,r){var i,o=[];for(i in e)o.push(i);for(i in n)o.indexOf(i)<0&&o.push(i);return e=o.map(function(t){return e[t]||0}),n=o.map(function(t){return n[t]||0}),[e,n,function(e){var n=e.map(function(n,i){return 1==e.length&&r&&(n=Math.max(n,0)),t.numberToString(n)+o[i]}).join(" + ");return e.length>1?"calc("+n+")":n}]}var a="px|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc",s=r.bind(null,new RegExp(a,"g")),u=r.bind(null,new RegExp(a+"|%","g")),c=r.bind(null,/deg|rad|grad|turn/g);t.parseLength=s,t.parseLengthOrPercent=u,t.consumeLengthOrPercent=t.consumeParenthesised.bind(null,u),t.parseAngle=c,t.mergeDimensions=o;var l=t.consumeParenthesised.bind(null,s),f=t.consumeRepeated.bind(void 0,l,/^/),d=t.consumeRepeated.bind(void 0,f,/^,/);t.consumeSizePairList=d;var h=function(t){var e=d(t);if(e&&""==e[1])return e[0]},p=t.mergeNestedRepeated.bind(void 0,i," "),m=t.mergeNestedRepeated.bind(void 0,p,",");t.mergeNonNegativeSizePair=p,t.addPropertiesHandler(h,m,["background-size"]),t.addPropertiesHandler(u,i,["border-bottom-width","border-image-width","border-left-width","border-right-width","border-top-width","flex-basis","font-size","height","line-height","max-height","max-width","outline-width","width"]),t.addPropertiesHandler(u,o,["border-bottom-left-radius","border-bottom-right-radius","border-top-left-radius","border-top-right-radius","bottom","left","letter-spacing","margin-bottom","margin-left","margin-right","margin-top","min-height","min-width","outline-offset","padding-bottom","padding-left","padding-right","padding-top","perspective","right","shape-margin","stroke-dashoffset","text-indent","top","vertical-align","word-spacing"])}(e),function(t,e){function n(e){return t.consumeLengthOrPercent(e)||t.consumeToken(/^auto/,e)}function r(e){var r=t.consumeList([t.ignore(t.consumeToken.bind(null,/^rect/)),t.ignore(t.consumeToken.bind(null,/^\(/)),t.consumeRepeated.bind(null,n,/^,/),t.ignore(t.consumeToken.bind(null,/^\)/))],e);if(r&&4==r[0].length)return r[0]}function i(e,n){return"auto"==e||"auto"==n?[!0,!1,function(r){var i=r?e:n;if("auto"==i)return"auto";var o=t.mergeDimensions(i,i);return o[2](o[0])}]:t.mergeDimensions(e,n)}function o(t){return"rect("+t+")"}var a=t.mergeWrappedNestedRepeated.bind(null,o,i,", ");t.parseBox=r,t.mergeBoxes=a,t.addPropertiesHandler(r,a,["clip"])}(e),function(t,e){function n(t){return function(e){var n=0;return t.map(function(t){return t===l?e[n++]:t})}}function r(t){return t}function i(e){if("none"==(e=e.toLowerCase().trim()))return[];for(var n,r=/\s*(\w+)\(([^)]*)\)/g,i=[],o=0;n=r.exec(e);){if(n.index!=o)return;o=n.index+n[0].length;var a=n[1],s=h[a];if(!s)return;var u=n[2].split(","),c=s[0];if(c.length<u.length)return;for(var l=[],p=0;p<c.length;p++){var m,g=u[p],v=c[p];if(void 0===(m=g?{A:function(e){return"0"==e.trim()?d:t.parseAngle(e)},N:t.parseNumber,T:t.parseLengthOrPercent,L:t.parseLength}[v.toUpperCase()](g):{a:d,n:l[0],t:f}[v]))return;l.push(m)}if(i.push({t:a,d:l}),r.lastIndex==e.length)return i}}function o(t){return t.toFixed(6).replace(".000000","")}function a(e,n){if(e.decompositionPair!==n){e.decompositionPair=n;var r=t.makeMatrixDecomposition(e)}if(n.decompositionPair!==e){n.decompositionPair=e;var i=t.makeMatrixDecomposition(n)}return null==r[0]||null==i[0]?[[!1],[!0],function(t){return t?n[0].d:e[0].d}]:(r[0].push(0),i[0].push(1),[r,i,function(e){var n=t.quat(r[0][3],i[0][3],e[5]);return t.composeMatrix(e[0],e[1],e[2],n,e[4]).map(o).join(",")}])}function s(t){return t.replace(/[xy]/,"")}function u(t){return t.replace(/(x|y|z|3d)?$/,"3d")}function c(e,n){var r=t.makeMatrixDecomposition&&!0,i=!1;if(!e.length||!n.length){e.length||(i=!0,e=n,n=[]);for(var o=0;o<e.length;o++){var c=e[o].t,l=e[o].d,f="scale"==c.substr(0,5)?1:0;n.push({t:c,d:l.map(function(t){if("number"==typeof t)return f;var e={};for(var n in t)e[n]=f;return e})})}}var d=[],p=[],m=[];if(e.length!=n.length){if(!r)return;var g=a(e,n);d=[g[0]],p=[g[1]],m=[["matrix",[g[2]]]]}else for(var o=0;o<e.length;o++){var c,v=e[o].t,b=n[o].t,_=e[o].d,y=n[o].d,T=h[v],x=h[b];if(function(t,e){return"perspective"==t&&"perspective"==e||("matrix"==t||"matrix3d"==t)&&("matrix"==e||"matrix3d"==e)}(v,b)){if(!r)return;var g=a([e[o]],[n[o]]);d.push(g[0]),p.push(g[1]),m.push(["matrix",[g[2]]])}else{if(v==b)c=v;else if(T[2]&&x[2]&&s(v)==s(b))c=s(v),_=T[2](_),y=x[2](y);else{if(!T[1]||!x[1]||u(v)!=u(b)){if(!r)return;var g=a(e,n);d=[g[0]],p=[g[1]],m=[["matrix",[g[2]]]];break}c=u(v),_=T[1](_),y=x[1](y)}for(var w=[],N=[],k=[],S=0;S<_.length;S++){var P="number"==typeof _[S]?t.mergeNumbers:t.mergeDimensions,g=P(_[S],y[S]);w[S]=g[0],N[S]=g[1],k.push(g[2])}d.push(w),p.push(N),m.push([c,k])}}if(i){var R=d;d=p,p=R}return[d,p,function(t){return t.map(function(t,e){var n=t.map(function(t,n){return m[e][1][n](t)}).join(",");return"matrix"==m[e][0]&&16==n.split(",").length&&(m[e][0]="matrix3d"),m[e][0]+"("+n+")"}).join(" ")}]}var l=null,f={px:0},d={deg:0},h={matrix:["NNNNNN",[l,l,0,0,l,l,0,0,0,0,1,0,l,l,0,1],r],matrix3d:["NNNNNNNNNNNNNNNN",r],rotate:["A"],rotatex:["A"],rotatey:["A"],rotatez:["A"],rotate3d:["NNNA"],perspective:["L"],scale:["Nn",n([l,l,1]),r],scalex:["N",n([l,1,1]),n([l,1])],scaley:["N",n([1,l,1]),n([1,l])],scalez:["N",n([1,1,l])],scale3d:["NNN",r],skew:["Aa",null,r],skewx:["A",null,n([l,d])],skewy:["A",null,n([d,l])],translate:["Tt",n([l,l,f]),r],translatex:["T",n([l,f,f]),n([l,f])],translatey:["T",n([f,l,f]),n([f,l])],translatez:["L",n([f,f,l])],translate3d:["TTL",r]};t.addPropertiesHandler(i,c,["transform"]),t.transformToSvgMatrix=function(e){var n=t.transformListToMatrix(i(e));return"matrix("+o(n[0])+" "+o(n[1])+" "+o(n[4])+" "+o(n[5])+" "+o(n[12])+" "+o(n[13])+")"}}(e),function(t){function e(t){var e=Number(t);if(!(isNaN(e)||e<100||e>900||e%100!=0))return e}function n(e){return e=100*Math.round(e/100),e=t.clamp(100,900,e),400===e?"normal":700===e?"bold":String(e)}function r(t,e){return[t,e,n]}t.addPropertiesHandler(e,r,["font-weight"])}(e),function(t){function e(t){var e={};for(var n in t)e[n]=-t[n];return e}function n(e){return t.consumeToken(/^(left|center|right|top|bottom)\b/i,e)||t.consumeLengthOrPercent(e)}function r(e,r){var i=t.consumeRepeated(n,/^/,r);if(i&&""==i[1]){var o=i[0];if(o[0]=o[0]||"center",o[1]=o[1]||"center",3==e&&(o[2]=o[2]||{px:0}),o.length==e){if(/top|bottom/.test(o[0])||/left|right/.test(o[1])){var s=o[0];o[0]=o[1],o[1]=s}if(/left|right|center|Object/.test(o[0])&&/top|bottom|center|Object/.test(o[1]))return o.map(function(t){return"object"==typeof t?t:a[t]})}}}function i(r){var i=t.consumeRepeated(n,/^/,r);if(i){for(var o=i[0],s=[{"%":50},{"%":50}],u=0,c=!1,l=0;l<o.length;l++){var f=o[l];"string"==typeof f?(c=/bottom|right/.test(f),u={left:0,right:0,center:u,top:1,bottom:1}[f],s[u]=a[f],"center"==f&&u++):(c&&(f=e(f),f["%"]=(f["%"]||0)+100),s[u]=f,u++,c=!1)}return[s,i[1]]}}function o(e){var n=t.consumeRepeated(i,/^,/,e);if(n&&""==n[1])return n[0]}var a={left:{"%":0},center:{"%":50},right:{"%":100},top:{"%":0},bottom:{"%":100}},s=t.mergeNestedRepeated.bind(null,t.mergeDimensions," ");t.addPropertiesHandler(r.bind(null,3),s,["transform-origin"]),t.addPropertiesHandler(r.bind(null,2),s,["perspective-origin"]),t.consumePosition=i,t.mergeOffsetList=s;var u=t.mergeNestedRepeated.bind(null,s,", ");t.addPropertiesHandler(o,u,["background-position","object-position"])}(e),function(t){function e(e){var n=t.consumeToken(/^circle/,e);if(n&&n[0])return["circle"].concat(t.consumeList([t.ignore(t.consumeToken.bind(void 0,/^\(/)),r,t.ignore(t.consumeToken.bind(void 0,/^at/)),t.consumePosition,t.ignore(t.consumeToken.bind(void 0,/^\)/))],n[1]));var o=t.consumeToken(/^ellipse/,e);if(o&&o[0])return["ellipse"].concat(t.consumeList([t.ignore(t.consumeToken.bind(void 0,/^\(/)),i,t.ignore(t.consumeToken.bind(void 0,/^at/)),t.consumePosition,t.ignore(t.consumeToken.bind(void 0,/^\)/))],o[1]));var a=t.consumeToken(/^polygon/,e);return a&&a[0]?["polygon"].concat(t.consumeList([t.ignore(t.consumeToken.bind(void 0,/^\(/)),t.optional(t.consumeToken.bind(void 0,/^nonzero\s*,|^evenodd\s*,/),"nonzero,"),t.consumeSizePairList,t.ignore(t.consumeToken.bind(void 0,/^\)/))],a[1])):void 0}function n(e,n){if(e[0]===n[0])return"circle"==e[0]?t.mergeList(e.slice(1),n.slice(1),["circle(",t.mergeDimensions," at ",t.mergeOffsetList,")"]):"ellipse"==e[0]?t.mergeList(e.slice(1),n.slice(1),["ellipse(",t.mergeNonNegativeSizePair," at ",t.mergeOffsetList,")"]):"polygon"==e[0]&&e[1]==n[1]?t.mergeList(e.slice(2),n.slice(2),["polygon(",e[1],a,")"]):void 0}var r=t.consumeParenthesised.bind(null,t.parseLengthOrPercent),i=t.consumeRepeated.bind(void 0,r,/^/),o=t.mergeNestedRepeated.bind(void 0,t.mergeDimensions," "),a=t.mergeNestedRepeated.bind(void 0,o,",");t.addPropertiesHandler(e,n,["shape-outside"])}(e),function(t,e){function n(t,e){e.concat([t]).forEach(function(e){e in document.documentElement.style&&(r[t]=e),i[e]=t})}var r={},i={};n("transform",["webkitTransform","msTransform"]),n("transformOrigin",["webkitTransformOrigin"]),n("perspective",["webkitPerspective"]),n("perspectiveOrigin",["webkitPerspectiveOrigin"]),t.propertyName=function(t){return r[t]||t},t.unprefixedPropertyName=function(t){return i[t]||t}}(e)}(),function(){if(void 0===document.createElement("div").animate([]).oncancel){var t;if(window.performance&&performance.now)var t=function(){return performance.now()};else var t=function(){return Date.now()};var e=function(t,e,n){this.target=t,this.currentTime=e,this.timelineTime=n,this.type="cancel",this.bubbles=!1,this.cancelable=!1,this.currentTarget=t,this.defaultPrevented=!1,this.eventPhase=Event.AT_TARGET,this.timeStamp=Date.now()},n=window.Element.prototype.animate;window.Element.prototype.animate=function(r,i){var o=n.call(this,r,i);o._cancelHandlers=[],o.oncancel=null;var a=o.cancel;o.cancel=function(){a.call(this);var n=new e(this,null,t()),r=this._cancelHandlers.concat(this.oncancel?[this.oncancel]:[]);setTimeout(function(){r.forEach(function(t){t.call(n.target,n)})},0)};var s=o.addEventListener;o.addEventListener=function(t,e){"function"==typeof e&&"cancel"==t?this._cancelHandlers.push(e):s.call(this,t,e)};var u=o.removeEventListener;return o.removeEventListener=function(t,e){if("cancel"==t){var n=this._cancelHandlers.indexOf(e);n>=0&&this._cancelHandlers.splice(n,1)}else u.call(this,t,e)},o}}}(),function(t){var e=document.documentElement,n=null,r=!1;try{var i=getComputedStyle(e).getPropertyValue("opacity"),o="0"==i?"1":"0";n=e.animate({opacity:[o,o]},{duration:1}),n.currentTime=0,r=getComputedStyle(e).getPropertyValue("opacity")==o}catch(t){}finally{n&&n.cancel()}if(!r){var a=window.Element.prototype.animate;window.Element.prototype.animate=function(e,n){return window.Symbol&&Symbol.iterator&&Array.prototype.from&&e[Symbol.iterator]&&(e=Array.from(e)),Array.isArray(e)||null===e||(e=t.convertToArrayForm(e)),a.call(this,e,n)}}}(t)}();}if (!("setImmediate"in self
)) {!function(e,t){"use strict";function n(e){return c[r]=a.apply(t,e),r++}function a(e){var n=[].slice.call(arguments,1);return function(){"function"==typeof e?e.apply(t,n):new Function(""+e)()}}function o(e){if(u)setTimeout(a(o,e),0);else{var t=c[e];if(t){u=!0;try{t()}finally{s(e),u=!1}}}}function s(e){delete c[e]}if(!e.setImmediate){var i,r=1,c={},u=!1,f=e.document,m=Object.getPrototypeOf&&Object.getPrototypeOf(e);m=m&&m.setTimeout?m:e,!function l(){if(e.postMessage&&!e.importScripts){var t=!0,n=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=n,t}}()?e.MessageChannel?function d(){var e=new MessageChannel;e.port1.onmessage=function(e){o(e.data)},i=function t(a){var o=n(arguments);return e.port2.postMessage(o),o}}():f&&"onreadystatechange"in f.createElement("script")?function p(){var e=f.documentElement;i=function t(a){var s=n(arguments),i=f.createElement("script");return i.onreadystatechange=function(){o(s),i.onreadystatechange=null,e.removeChild(i),i=null},e.appendChild(i),s}}():function g(){i=function e(t){var s=n(arguments);return setTimeout(a(o,s),0),s}}():function v(){var t="setImmediate$"+Math.random()+"$",a=function(n){n.source===e&&"string"==typeof n.data&&0===n.data.indexOf(t)&&o(+n.data.slice(t.length))};e.addEventListener?e.addEventListener("message",a,!1):e.attachEvent("onmessage",a),i=function s(a){var o=n(arguments);return e.postMessage(t+o,"*"),o}}(),m.setImmediate=i,m.clearImmediate=s}}(self);}if (!((function(){if("document"in self&&"documentElement"in self.document&&"style"in self.document.documentElement&&"scrollBehavior"in document.documentElement.style)return!0
if(Element.prototype.scrollTo&&Element.prototype.scrollTo.toString().indexOf("[native code]")>-1)return!1
try{var e=!1,t={top:1,left:0}
Object.defineProperty(t,"behavior",{get:function(){return e=!0,"smooth"},enumerable:!0})
var o=document.createElement("DIV"),n=document.createElement("DIV")
return o.setAttribute("style","height: 1px; overflow: scroll;"),n.setAttribute("style","height: 2px; overflow: scroll;"),o.appendChild(n),o.scrollTo(t),e}catch(r){return!1}})()
)) {!function(e,t){var n={};!function(e){"use strict";function t(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),l=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)l.push(r.value)}catch(c){o={error:c}}finally{try{r&&!r.done&&(n=i["return"])&&n.call(i)}finally{if(o)throw o.error}}return l}var n=function(e){return.5*(1-Math.cos(Math.PI*e))},r=function(){return"scrollBehavior"in document.documentElement.style},o={_elementScroll:undefined,get elementScroll(){return this._elementScroll||(this._elementScroll=HTMLElement.prototype.scroll||HTMLElement.prototype.scrollTo||function(e,t){this.scrollLeft=e,this.scrollTop=t})},_elementScrollIntoView:undefined,get elementScrollIntoView(){return this._elementScrollIntoView||(this._elementScrollIntoView=HTMLElement.prototype.scrollIntoView)},_windowScroll:undefined,get windowScroll(){return this._windowScroll||(this._windowScroll=window.scroll||window.scrollTo)}},i=function(e){[HTMLElement.prototype,SVGElement.prototype,Element.prototype].forEach(function(t){return e(t)})},l=function(){var e,t,n;return null!==(n=null===(t=null===(e=window.performance)||void 0===e?void 0:e.now)||void 0===t?void 0:t.call(e))&&void 0!==n?n:Date.now()},c=function(e){var t=l(),r=(t-e.timeStamp)/(e.duration||500);if(r>1)return e.method(e.targetX,e.targetY),void e.callback();var o=(e.timingFunc||n)(r),i=e.startX+(e.targetX-e.startX)*o,a=e.startY+(e.targetY-e.startY)*o;e.method(i,a),e.rafId=requestAnimationFrame(function(){c(e)})},a=function(e){return isFinite(e)?Number(e):0},u=function(e){var t=typeof e;return null!==e&&("object"===t||"function"===t)},s=function(){return s=Object.assign||function e(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},s.apply(this,arguments)},f=function(e,t){var n,r,i=o.elementScroll.bind(e);if(t.left!==undefined||t.top!==undefined){var u=e.scrollLeft,s=e.scrollTop,f=a(null!==(n=t.left)&&void 0!==n?n:u),d=a(null!==(r=t.top)&&void 0!==r?r:s);if("smooth"!==t.behavior)return i(f,d);var w=function(){window.removeEventListener("wheel",p),window.removeEventListener("touchmove",p)},m={timeStamp:l(),duration:t.duration,startX:u,startY:s,targetX:f,targetY:d,rafId:0,method:i,timingFunc:t.timingFunc,callback:w},p=function(){cancelAnimationFrame(m.rafId),w()};window.addEventListener("wheel",p,{passive:!0,once:!0}),window.addEventListener("touchmove",p,{passive:!0,once:!0}),c(m)}},d=function(e){if(!r()){var t=o.elementScroll;i(function(n){return n.scroll=function r(){if(1===arguments.length){var n=arguments[0];if(!u(n))throw new TypeError("Failed to execute 'scroll' on 'Element': parameter 1 ('options') is not an object.");return f(this,s(s({},n),e))}return t.apply(this,arguments)}})}},w=function(e,t){var n=a(t.left||0)+e.scrollLeft,r=a(t.top||0)+e.scrollTop;return f(e,s(s({},t),{left:n,top:r}))},m=function(e){r()||i(function(t){return t.scrollBy=function n(){if(1===arguments.length){var t=arguments[0];if(!u(t))throw new TypeError("Failed to execute 'scrollBy' on 'Element': parameter 1 ('options') is not an object.");return w(this,s(s({},t),e))}var n=Number(arguments[0]),r=Number(arguments[1]);return w(this,{left:n,top:r})}})},p=function(e){switch(e){case"horizontal-tb":case"lr":case"lr-tb":case"rl":case"rl-tb":return 0;case"vertical-rl":case"tb":case"tb-rl":return 1;case"vertical-lr":case"tb-lr":return 2;case"sideways-rl":return 3;case"sideways-lr":return 4}return 0},h=function(e,n,r){var o,i=t([e.block||"start",e.inline||"nearest"],2),l=i[0],c=i[1],a=0;switch(r||(a^=2),n){case 0:a=a>>1|(1&a)<<1,o=t([c,l],2),l=o[0],c=o[1];break;case 1:case 3:a^=1;break;case 4:a^=2}return[l,c].map(function(e,t){switch(e){case"center":return 1;case"nearest":return 0;default:return"start"===e==!(a>>t&1)?2:3}})},v=function(e,t,n,r,o,i,l,c){return i<e&&l>t||i>e&&l<t?0:i<=e&&c<=n||l>=t&&c>=n?i-e-r:l>t&&c<n||i<e&&c>n?l-t+o:0},b=function(e){return"visible"!==e&&"clip"!==e},g=function(e){if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(t){return null}},y=function(e){var t=g(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)},S=function(e,t){return(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth)&&(b(t.overflowY)||b(t.overflowX)||y(e))},E=function(e){var t=e.parentNode;return null!==t&&t.nodeType===Node.DOCUMENT_FRAGMENT_NODE?t.host:t},T=function(e,t){return e<-t?-t:e>t?t:e},k=function(e){return e in document.documentElement.style},I=function(){return["scroll-margin","scroll-snap-margin"].filter(k)[0]},V=function(e,n){var r=e.getBoundingClientRect(),o=r.top,i=r.right,l=r.bottom,c=r.left,a=t(["top","right","bottom","left"].map(function(e){var t=I(),r=n.getPropertyValue(t+"-"+e);return parseInt(r,10)||0}),4);return[o-a[0],i+a[1],l+a[2],c-a[3]]},L=function(e,n){if(!1!==e.isConnected){for(var r=document.scrollingElement||document.documentElement,o=[],i=getComputedStyle(document.documentElement),l=E(e);null!==l;l=E(l)){if(l===r){o.push(l);break}var c=getComputedStyle(l);if((l!==document.body||!S(l,c)||S(document.documentElement,i))&&(S(l,c)&&o.push(l),"fixed"===c.position))break}var a=window.visualViewport?window.visualViewport.width:innerWidth,u=window.visualViewport?window.visualViewport.height:innerHeight,d=window.scrollX||window.pageXOffset,w=window.scrollY||window.pageYOffset,m=getComputedStyle(e),b=t(V(e,m),4),g=b[0],y=b[1],k=b[2],I=b[3],L=k-g,j=y-I,F=p(m.writingMode||m.getPropertyValue("-webkit-writing-mode")||m.getPropertyValue("-ms-writing-mode")),W="rtl"!==m.direction,O=t(h(n,F,W),2),X=O[0],Y=O[1],N=function(){switch(Y){case 1:return g+L/2;case 2:case 0:return g;case 3:return k}}(),H=function(){switch(X){case 1:return I+j/2;case 3:return y;case 2:case 0:return I}}(),P=[];o.forEach(function(e){var t=e.getBoundingClientRect(),o=t.height,i=t.width,l=t.top,c=t.right,m=t.bottom,p=t.left,h=getComputedStyle(e),b=parseInt(h.borderLeftWidth,10),g=parseInt(h.borderTopWidth,10),y=parseInt(h.borderRightWidth,10),S=parseInt(h.borderBottomWidth,10),E=0,k=0,I="offsetWidth"in e?e.offsetWidth-e.clientWidth-b-y:0,V="offsetHeight"in e?e.offsetHeight-e.clientHeight-g-S:0;if(r===e){switch(Y){case 2:E=N;break;case 3:E=N-u;break;case 1:E=N-u/2;break;case 0:E=v(w,w+u,u,g,S,w+N,w+N+L,L)}switch(X){case 2:k=H;break;case 3:k=H-a;break;case 1:k=H-a/2;break;case 0:k=v(d,d+a,a,b,y,d+H,d+H+j,j)}E+=w,k+=d}else{switch(Y){case 2:E=N-l-g;break;case 3:E=N-m+S+V;break;case 1:E=N-(l+o/2)+V/2;break;case 0:E=v(l,m,o,g,S+V,N,N+L,L)}switch(X){case 2:k=H-p-b;break;case 3:k=H-c+y+I;break;case 1:k=H-(p+i/2)+I/2;break;case 0:k=v(p,c,i,b,y+I,H,H+j,j)}var F=e.scrollLeft,W=e.scrollTop;E=T(W+E,e.scrollHeight-o+V),k=T(F+k,e.scrollWidth-i+I),N+=W-E,H+=F-k}P.push(function(){return f(e,s(s({},n),{top:E,left:k}))})}),P.forEach(function(e){return e()})}},j=function(e){if(!r()){var t=o.elementScrollIntoView;i(function(n){return n.scrollIntoView=function r(){var n=arguments[0];return 1===arguments.length&&u(n)?L(this,s(s({},n),e)):t.apply(this,arguments)}})}},F=function(e){if(!r()){var t=o.elementScroll;i(function(n){return n.scrollTo=function r(){if(1===arguments.length){var n=arguments[0];if(!u(n))throw new TypeError("Failed to execute 'scrollTo' on 'Element': parameter 1 ('options') is not an object.");var r=Number(n.left),o=Number(n.top);return f(this,s(s(s({},n),{left:r,top:o}),e))}return t.apply(this,arguments)}})}},W=function(e){var t,n,r=o.windowScroll.bind(window);if(e.left!==undefined||e.top!==undefined){var i=window.scrollX||window.pageXOffset,u=window.scrollY||window.pageYOffset,s=a(null!==(t=e.left)&&void 0!==t?t:i),f=a(null!==(n=e.top)&&void 0!==n?n:u);if("smooth"!==e.behavior)return r(s,f);var d=function(){window.removeEventListener("wheel",m),window.removeEventListener("touchmove",m)},w={timeStamp:l(),duration:e.duration,startX:i,startY:u,targetX:s,targetY:f,rafId:0,method:r,timingFunc:e.timingFunc,callback:d},m=function(){cancelAnimationFrame(w.rafId),d()};window.addEventListener("wheel",m,{passive:!0,once:!0}),window.addEventListener("touchmove",m,{passive:!0,once:!0}),c(w)}},O=function(e){if(!r()){var t=o.windowScroll;window.scroll=function n(){if(1===arguments.length){var n=arguments[0];if(!u(n))throw new TypeError("Failed to execute 'scroll' on 'Window': parameter 1 ('options') is not an object.");return W(s(s({},n),e))}return t.apply(this,arguments)}}},X=function(e){var t=a(e.left||0)+(window.scrollX||window.pageXOffset),n=a(e.top||0)+(window.scrollY||window.pageYOffset);return"smooth"!==e.behavior?o.windowScroll.call(window,t,n):W(s(s({},e),{left:t,top:n}))},Y=function(e){r()||(window.scrollBy=function t(){if(1===arguments.length){var t=arguments[0];if(!u(t))throw new TypeError("Failed to execute 'scrollBy' on 'Window': parameter 1 ('options') is not an object.");return X(s(s({},t),e))}var n=Number(arguments[0]),r=Number(arguments[1]);return X({left:n,top:r})})},N=function(e){if(!r()){var t=o.windowScroll;window.scrollTo=function n(){if(1===arguments.length){var n=arguments[0];if(!u(n))throw new TypeError("Failed to execute 'scrollTo' on 'Window': parameter 1 ('options') is not an object.");var r=Number(n.left),o=Number(n.top);return W(s(s(s({},n),{left:r,top:o}),e))}return t.apply(this,arguments)}}},H=function(e){r()||(O(e),N(e),Y(e),d(e),F(e),m(e),j(e))};e.elementScroll=f,e.elementScrollBy=w,e.elementScrollByPolyfill=m,e.elementScrollIntoView=L,e.elementScrollIntoViewPolyfill=j,e.elementScrollPolyfill=d,e.elementScrollTo=f,e.elementScrollToPolyfill=F,e.polyfill=H,e.seamless=H,e.windowScroll=W,e.windowScrollBy=X,e.windowScrollByPolyfill=Y,e.windowScrollPolyfill=O,e.windowScrollTo=W,e.windowScrollToPolyfill=N,Object.defineProperty(e,"__esModule",{value:!0})}(n),n.polyfill()}();}if (!((function(){var e=document.createElement("p"),t=!1
return e.innerHTML="<section></section>",document.documentElement.appendChild(e),e.firstChild&&("getComputedStyle"in window?t="block"===getComputedStyle(e.firstChild).display:e.firstChild.currentStyle&&(t="block"===e.firstChild.currentStyle.display)),document.documentElement.removeChild(e),t})()
)) {!function(e,t){function n(e,t){var n=e.createElement("p"),r=e.getElementsByTagName("head")[0]||e.documentElement;return n.innerHTML="x<style>"+t+"</style>",r.insertBefore(n.lastChild,r.firstChild)}function r(){var e=y.elements;return"string"==typeof e?e.split(" "):e}function a(e,t){var n=y.elements;"string"!=typeof n&&(n=n.join(" ")),"string"!=typeof e&&(e=e.join(" ")),y.elements=n+" "+e,m(t)}function o(e){var t=v[e[p]];return t||(t={},g++,e[p]=g,v[g]=t),t}function c(e,n,r){if(n||(n=t),s)return n.createElement(e);r||(r=o(n));var a;return a=r.cache[e]?r.cache[e].cloneNode():h.test(e)?(r.cache[e]=r.createElem(e)).cloneNode():r.createElem(e),!a.canHaveChildren||f.test(e)||a.tagUrn?a:r.frag.appendChild(a)}function i(e,n){if(e||(e=t),s)return e.createDocumentFragment();n=n||o(e);for(var a=n.frag.cloneNode(),c=0,i=r(),l=i.length;l>c;c++)a.createElement(i[c]);return a}function l(e,t){t.cache||(t.cache={},t.createElem=e.createElement,t.createFrag=e.createDocumentFragment,t.frag=t.createFrag()),e.createElement=function(n){return y.shivMethods?c(n,e,t):t.createElem(n)},e.createDocumentFragment=Function("h,f","return function(){var n=f.cloneNode(),c=n.createElement;h.shivMethods&&("+r().join().replace(/[\w\-:]+/g,function(e){return t.createElem(e),t.frag.createElement(e),'c("'+e+'")'})+");return n}")(y,t.frag)}function m(e){e||(e=t);var r=o(e);return!y.shivCSS||u||r.hasCSS||(r.hasCSS=!!n(e,"article,aside,dialog,figcaption,figure,footer,header,hgroup,main,nav,section{display:block}mark{background:#FF0;color:#000}template{display:none}")),s||l(e,r),e}var u,s,d=e.html5||{},f=/^<|^(?:button|map|select|textarea|object|iframe|option|optgroup)$/i,h=/^(?:a|b|code|div|fieldset|h1|h2|h3|h4|h5|h6|i|label|li|ol|p|q|span|strong|style|table|tbody|td|th|tr|ul)$/i,p="_html5shiv",g=0,v={};!function(){try{var e=t.createElement("a");e.innerHTML="<xyz></xyz>",u="hidden"in e,s=1==e.childNodes.length||function(){t.createElement("a");var e=t.createDocumentFragment();return"undefined"==typeof e.cloneNode||"undefined"==typeof e.createDocumentFragment||"undefined"==typeof e.createElement}()}catch(n){u=!0,s=!0}}();var y={elements:d.elements||"abbr article aside audio bdi canvas data datalist details dialog figcaption figure footer header hgroup main mark meter nav output picture progress section summary template time video",version:"3.7.3-pre",shivCSS:!1!==d.shivCSS,supportsUnknownElements:s,shivMethods:!1!==d.shivMethods,type:"default",shivDocument:m,createElement:c,createDocumentFragment:i,addElements:a};e.html5=y,m(t),"object"==typeof module&&module.exports&&(module.exports=y)}("undefined"!=typeof window?window:this,document);}if (!("HTMLPictureElement"in self||"picturefill"in self
)) {!function(e){var t=navigator.userAgent;e.HTMLPictureElement&&/ecko/.test(t)&&t.match(/rv\:(\d+)/)&&RegExp.$1<45&&addEventListener("resize",function(){var t,n=document.createElement("source"),r=function(e){var t,r,s=e.parentNode;"PICTURE"===s.nodeName.toUpperCase()?(t=n.cloneNode(),s.insertBefore(t,s.firstElementChild),setTimeout(function(){s.removeChild(t)})):(!e._pfLastSize||e.offsetWidth>e._pfLastSize)&&(e._pfLastSize=e.offsetWidth,r=e.sizes,e.sizes+=",100vw",setTimeout(function(){e.sizes=r}))},s=function(){var e,t=document.querySelectorAll("picture > img, img[srcset][sizes]");for(e=0;e<t.length;e++)r(t[e])},i=function(){clearTimeout(t),t=setTimeout(s,99)},c=e.matchMedia&&matchMedia("(orientation: landscape)"),a=function(){i(),c&&c.addListener&&c.addListener(i)};return n.srcset="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",/^[c|i]|d$/.test(document.readyState||"")?a():document.addEventListener("DOMContentLoaded",a),i}())}(window),function(e,t,n){"use strict";function r(e){return" "===e||"\t"===e||"\n"===e||"\f"===e||"\r"===e}function s(){k=!1,B=e.devicePixelRatio,O={},D={},g.DPR=B||1,H.width=Math.max(e.innerWidth||0,y.clientWidth),H.height=Math.max(e.innerHeight||0,y.clientHeight),H.vw=H.width/100,H.vh=H.height/100,h=[H.height,H.width,B].join("-"),H.em=g.getEmValue(),H.rem=H.em}function i(e,t,n,r){var s,i,c,a;return"saveData"===M.algorithm?e>2.7?a=n+1:(i=t-n,s=Math.pow(e-.6,1.5),c=i*s,r&&(c+=.1*s),a=e+c):a=n>1?Math.sqrt(e*t):e,a>n}function c(e){var t,n=g.getSet(e),r=!1;"pending"!==n&&(r=h,n&&(t=g.setRes(n),g.applySetCandidate(t,e))),e[g.ns].evaled=r}function a(e,t){return e.res-t.res}function o(e,t,n){var r;return!n&&t&&(n=e[g.ns].sets,n=n&&n[n.length-1]),r=u(t,n),r&&(t=g.makeUrl(t),e[g.ns].curSrc=t,e[g.ns].curCan=r,r.res||X(r,r.set.sizes)),r}function u(e,t){var n,r,s;if(e&&t)for(s=g.parseSet(t),e=g.makeUrl(e),n=0;n<s.length;n++)if(e===g.makeUrl(s[n].url)){r=s[n];break}return r}function d(e,t){var n,r,s,i,c=e.getElementsByTagName("source");for(n=0,r=c.length;r>n;n++)s=c[n],s[g.ns]=!0,(i=s.getAttribute("srcset"))&&t.push({srcset:i,media:s.getAttribute("media"),type:s.getAttribute("type"),sizes:s.getAttribute("sizes")})}function l(e,t){function n(t){var n,r=t.exec(e.substring(l));return r?(n=r[0],l+=n.length,n):void 0}function s(){var e,n,r,s,a,o,u,d,l,p=!1,m={};for(s=0;s<c.length;s++)a=c[s],o=a[a.length-1],u=a.substring(0,a.length-1),d=parseInt(u,10),l=parseFloat(u),q.test(u)&&"w"===o?((e||n)&&(p=!0),0===d?p=!0:e=d):_.test(u)&&"x"===o?((e||n||r)&&(p=!0),0>l?p=!0:n=l):q.test(u)&&"h"===o?((r||n)&&(p=!0),0===d?p=!0:r=d):p=!0;p||(m.url=i,e&&(m.w=e),n&&(m.d=n),r&&(m.h=r),r||n||e||(m.d=1),1===m.d&&(t.has1x=!0),m.set=t,f.push(m))}for(var i,c,a,o,u,d=e.length,l=0,f=[];;){if(n(F),l>=d)return f;i=n(W),c=[],","===i.slice(-1)?(i=i.replace(Q,""),s()):function p(){for(n(G),a="",o="in descriptor";;){if(u=e.charAt(l),"in descriptor"===o)if(r(u))a&&(c.push(a),a="",o="after descriptor");else{if(","===u)return l+=1,a&&c.push(a),void s();if("("===u)a+=u,o="in parens";else{if(""===u)return a&&c.push(a),void s();a+=u}}else if("in parens"===o)if(")"===u)a+=u,o="in descriptor";else{if(""===u)return c.push(a),void s();a+=u}else if("after descriptor"===o)if(r(u));else{if(""===u)return void s();o="in descriptor",l-=1}l+=1}}()}}function f(e){var t,n,s,i,c,a,o=/^(?:[+-]?[0-9]+|[0-9]*\.[0-9]+)(?:[eE][+-]?[0-9]+)?(?:ch|cm|em|ex|in|mm|pc|pt|px|rem|vh|vmin|vmax|vw)$/i,u=/^calc\((?:[0-9a-z \.\+\-\*\/\(\)]+)\)$/i;for(n=function d(e){function t(){i&&(c.push(i),i="")}function n(){c[0]&&(a.push(c),c=[])}for(var s,i="",c=[],a=[],o=0,u=0,d=!1;;){if(""===(s=e.charAt(u)))return t(),n(),a;if(d){if("*"===s&&"/"===e[u+1]){d=!1,u+=2,t();continue}u+=1}else{if(r(s)){if(e.charAt(u-1)&&r(e.charAt(u-1))||!i){u+=1;continue}if(0===o){t(),u+=1;continue}s=" "}else if("("===s)o+=1;else if(")"===s)o-=1;else{if(","===s){t(),n(),u+=1;continue}if("/"===s&&"*"===e.charAt(u+1)){d=!0,u+=2;continue}}i+=s,u+=1}}}(e),s=n.length,t=0;s>t;t++)if(i=n[t],c=i[i.length-1],function l(e){return!!(o.test(e)&&parseFloat(e)>=0)||(!!u.test(e)||("0"===e||"-0"===e||"+0"===e))}(c)){if(a=c,i.pop(),0===i.length)return a;if(i=i.join(" "),g.matchesMedia(i))return a}return"100vw"}t.createElement("picture");var p,m,h,g={},A=!1,v=function(){},w=t.createElement("img"),S=w.getAttribute,b=w.setAttribute,E=w.removeAttribute,y=t.documentElement,T={},M={algorithm:""},C="data-pfsrc",x=C+"set",R=navigator.userAgent,z=/rident/.test(R)||/ecko/.test(R)&&R.match(/rv\:(\d+)/)&&RegExp.$1>35,L="currentSrc",I=/\s+\+?\d+(e\d+)?w/,N=/(\([^)]+\))?\s*(.+)/,P=e.picturefillCFG,U="font-size:100%!important;",k=!0,O={},D={},B=e.devicePixelRatio,H={px:1,"in":96},$=t.createElement("a"),j=!1,G=/^[ \t\n\r\u000c]+/,F=/^[, \t\n\r\u000c]+/,W=/^[^ \t\n\r\u000c]+/,Q=/[,]+$/,q=/^\d+$/,_=/^-?(?:[0-9]+|[0-9]*\.[0-9]+)(?:[eE][+-]?[0-9]+)?$/,V=function(e,t,n,r){e.addEventListener?e.addEventListener(t,n,r||!1):e.attachEvent&&e.attachEvent("on"+t,n)},K=function(e){var t={};return function(n){return n in t||(t[n]=e(n)),t[n]}},J=function(){var e=/^([\d\.]+)(em|vw|px)$/,t=function(){for(var e=arguments,t=0,n=e[0];++t in e;)n=n.replace(e[t],e[++t]);return n},n=K(function(e){return"return "+t((e||"").toLowerCase(),/\band\b/g,"&&",/,/g,"||",/min-([a-z-\s]+):/g,"e.$1>=",/max-([a-z-\s]+):/g,"e.$1<=",/calc([^)]+)/g,"($1)",/(\d+[\.]*[\d]*)([a-z]+)/g,"($1 * e.$2)",/^(?!(e.[a-z]|[0-9\.&=|><\+\-\*\(\)\/])).*/gi,"")+";"});return function(t,r){var s;if(!(t in O))if(O[t]=!1,r&&(s=t.match(e)))O[t]=s[1]*H[s[2]];else try{O[t]=new Function("e",n(t))(H)}catch(s){}return O[t]}}(),X=function(e,t){return e.w?(e.cWidth=g.calcListLength(t||"100vw"),e.res=e.w/e.cWidth):e.res=e.d,e},Y=function(e){if(A){var n,r,s,i=e||{};if(i.elements&&1===i.elements.nodeType&&("IMG"===i.elements.nodeName.toUpperCase()?i.elements=[i.elements]:(i.context=i.elements,i.elements=null)),n=i.elements||g.qsa(i.context||t,i.reevaluate||i.reselect?g.sel:g.selShort),s=n.length){for(g.setupRun(i),j=!0,r=0;s>r;r++)g.fillImg(n[r],i);g.teardownRun(i)}}};e.console&&console.warn,L in w||(L="src"),T["image/jpeg"]=!0,T["image/gif"]=!0,T["image/png"]=!0,T["image/svg+xml"]=t.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#Image","1.1"),g.ns=("pf"+(new Date).getTime()).substr(0,9),g.supSrcset="srcset"in w,g.supSizes="sizes"in w,g.supPicture=!!e.HTMLPictureElement,g.supSrcset&&g.supPicture&&!g.supSizes&&function(e){w.srcset="data:,a",e.src="data:,a",g.supSrcset=w.complete===e.complete,g.supPicture=g.supSrcset&&g.supPicture}(t.createElement("img")),g.supSrcset&&!g.supSizes?function(){var e="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",n=t.createElement("img"),r=function(){2===n.width&&(g.supSizes=!0),m=g.supSrcset&&!g.supSizes,A=!0,setTimeout(Y)};n.onload=r,n.onerror=r,n.setAttribute("sizes","9px"),n.srcset=e+" 1w,data:image/gif;base64,R0lGODlhAgABAPAAAP///wAAACH5BAAAAAAALAAAAAACAAEAAAICBAoAOw== 9w",n.src=e}():A=!0,g.selShort="picture>img,img[srcset]",g.sel=g.selShort,g.cfg=M,g.DPR=B||1,g.u=H,g.types=T,g.setSize=v,g.makeUrl=K(function(e){return $.href=e,$.href}),g.qsa=function(e,t){return"querySelector"in e?e.querySelectorAll(t):[]},g.matchesMedia=function(){return e.matchMedia&&(matchMedia("(min-width: 0.1em)")||{}).matches?g.matchesMedia=function(e){return!e||matchMedia(e).matches}:g.matchesMedia=g.mMQ,g.matchesMedia.apply(this,arguments)},g.mMQ=function(e){return!e||J(e)},g.calcLength=function(e){var t=J(e,!0)||!1;return 0>t&&(t=!1),t},g.supportsType=function(e){return!e||T[e]},g.parseSize=K(function(e){var t=(e||"").match(N);return{media:t&&t[1],length:t&&t[2]}}),g.parseSet=function(e){return e.cands||(e.cands=l(e.srcset,e)),e.cands},g.getEmValue=function(){var e;if(!p&&(e=t.body)){var n=t.createElement("div"),r=y.style.cssText,s=e.style.cssText;n.style.cssText="position:absolute;left:0;visibility:hidden;display:block;padding:0;border:none;font-size:1em;width:1em;overflow:hidden;clip:rect(0px, 0px, 0px, 0px)",y.style.cssText=U,e.style.cssText=U,e.appendChild(n),p=n.offsetWidth,e.removeChild(n),p=parseFloat(p,10),y.style.cssText=r,e.style.cssText=s}return p||16},g.calcListLength=function(e){if(!(e in D)||M.uT){var t=g.calcLength(f(e));D[e]=t||H.width}return D[e]},g.setRes=function(e){var t;if(e){t=g.parseSet(e);for(var n=0,r=t.length;r>n;n++)X(t[n],e.sizes)}return t},g.setRes.res=X,g.applySetCandidate=function(e,t){if(e.length){var n,r,s,c,u,d,l,f,p,m=t[g.ns],h=g.DPR;if(d=m.curSrc||t[L],l=m.curCan||o(t,d,e[0].set),l&&l.set===e[0].set&&((p=z&&!t.complete&&l.res-.1>h)||(l.cached=!0,l.res>=h&&(u=l))),!u)for(e.sort(a),c=e.length,u=e[c-1],r=0;c>r;r++)if(n=e[r],n.res>=h){s=r-1,u=e[s]&&(p||d!==g.makeUrl(n.url))&&i(e[s].res,n.res,h,e[s].cached)?e[s]:n;break}u&&(f=g.makeUrl(u.url),m.curSrc=f,m.curCan=u,f!==d&&g.setSrc(t,u),g.setSize(t))}},g.setSrc=function(e,t){var n;e.src=t.url,"image/svg+xml"===t.set.type&&(n=e.style.width,e.style.width=e.offsetWidth+1+"px",e.offsetWidth+1&&(e.style.width=n))},g.getSet=function(e){var t,n,r,s=!1,i=e[g.ns].sets;for(t=0;t<i.length&&!s;t++)if(n=i[t],n.srcset&&g.matchesMedia(n.media)&&(r=g.supportsType(n.type))){"pending"===r&&(n=r),s=n;break}return s},g.parseSets=function(e,t,r){var s,i,c,a,o=t&&"PICTURE"===t.nodeName.toUpperCase(),l=e[g.ns];(l.src===n||r.src)&&(l.src=S.call(e,"src"),l.src?b.call(e,C,l.src):E.call(e,C)),(l.srcset===n||r.srcset||!g.supSrcset||e.srcset)&&(s=S.call(e,"srcset"),l.srcset=s,a=!0),l.sets=[],o&&(l.pic=!0,d(t,l.sets)),l.srcset?(i={srcset:l.srcset,sizes:S.call(e,"sizes")},l.sets.push(i),(c=(m||l.src)&&I.test(l.srcset||""))||!l.src||u(l.src,i)||i.has1x||(i.srcset+=", "+l.src,i.cands.push({url:l.src,d:1,set:i}))):l.src&&l.sets.push({srcset:l.src,sizes:null}),l.curCan=null,l.curSrc=n,l.supported=!(o||i&&!g.supSrcset||c&&!g.supSizes),a&&g.supSrcset&&!l.supported&&(s?(b.call(e,x,s),e.srcset=""):E.call(e,x)),l.supported&&!l.srcset&&(!l.src&&e.src||e.src!==g.makeUrl(l.src))&&(null===l.src?e.removeAttribute("src"):e.src=l.src),l.parsed=!0},g.fillImg=function(e,t){var n,r=t.reselect||t.reevaluate;e[g.ns]||(e[g.ns]={}),n=e[g.ns],(r||n.evaled!==h)&&((!n.parsed||t.reevaluate)&&g.parseSets(e,e.parentNode,t),n.supported?n.evaled=h:c(e))},g.setupRun=function(){(!j||k||B!==e.devicePixelRatio)&&s()},g.supPicture?(Y=v,g.fillImg=v):function(){var n,r=e.attachEvent?/d$|^c/:/d$|^c|^i/,s=function(){var e=t.readyState||"";i=setTimeout(s,"loading"===e?200:999),t.body&&(g.fillImgs(),(n=n||r.test(e))&&clearTimeout(i))},i=setTimeout(s,t.body?9:99),c=y.clientHeight,a=function(){k=Math.max(e.innerWidth||0,y.clientWidth)!==H.width||y.clientHeight!==c,c=y.clientHeight,k&&g.fillImgs()};V(e,"resize",function(e,t){var n,r,s=function(){var i=new Date-r;t>i?n=setTimeout(s,t-i):(n=null,e())};return function(){r=new Date,n||(n=setTimeout(s,t))}}(a,99)),V(t,"readystatechange",s)}(),g.picturefill=Y,g.fillImgs=Y,g.teardownRun=v,Y._=g,e.picturefillCFG={pf:g,push:function(e){var t=e.shift();"function"==typeof g[t]?g[t].apply(g,e):(M[t]=e[0],j&&g.fillImgs({reselect:!0}))}};for(;P&&P.length;)e.picturefillCFG.push(P.shift());e.picturefill=Y,"object"==typeof module&&"object"==typeof module.exports?module.exports=Y:"function"==typeof define&&define.amd&&define("picturefill",function(){return Y}),g.supPicture||(T["image/webp"]=function Z(t,n){var r=new e.Image;return r.onerror=function(){T[t]=!1,Y()},r.onload=function(){T[t]=1===r.width,Y()},r.src=n,"pending"}("image/webp","data:image/webp;base64,UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAABBxAR/Q9ERP8DAABWUDggGAAAADABAJ0BKgEAAQADADQlpAADcAD++/1QAA=="))}(window,document),function(e){"use strict";var t,n=0,r=function(){window.picturefill&&e(window.picturefill),(window.picturefill||n>9999)&&clearInterval(t),n++};t=setInterval(r,8),r()}(function(e){"use strict";var t=window.document,n=window.Element,r=window.MutationObserver,s=function(){},i={disconnect:s,take:s,observe:s,start:s,stop:s,connected:!1},c=/^loade|^c|^i/.test(t.readyState||""),a=e._;if(a.mutationSupport=!1,a.observer=i,Object.keys&&window.HTMLSourceElement&&t.addEventListener){var o,u,d,l,f={src:1,srcset:1,sizes:1,media:1},p=Object.keys(f),m={attributes:!0,childList:!0,subtree:!0,attributeFilter:p},h=n&&n.prototype,g={},A=function(e,t){g[e]=a[e],a[e]=t};h&&!h.matches&&(h.matches=h.matchesSelector||h.mozMatchesSelector||h.webkitMatchesSelector||h.msMatchesSelector),h&&h.matches&&(o=function(e,t){return e.matches(t)},a.mutationSupport=!(!Object.create||!Object.defineProperties)),a.mutationSupport&&(i.observe=function(){d&&(i.connected=!0,u&&u.observe(t.documentElement,m))},i.disconnect=function(){i.connected=!1,u&&u.disconnect()},i.take=function(){u?a.onMutations(u.takeRecords()):l&&l.take()},i.start=function(){d=!0,i.observe()},i.stop=function(){d=!1,i.disconnect()},A("setupRun",function(){return i.disconnect(),g.setupRun.apply(this,arguments)}),A("teardownRun",function(){var e=g.setupRun.apply(this,arguments);return i.observe(),e}),A("setSrc",function(){var e,t=i.connected;return i.disconnect(),e=g.setSrc.apply(this,arguments),t&&i.observe(),e}),a.onMutations=function(e){var t,n,r=[];for(t=0,n=e.length;n>t;t++)c&&"childList"===e[t].type?a.onSubtreeChange(e[t],r):"attributes"===e[t].type&&a.onAttrChange(e[t],r);r.length&&a.fillImgs({elements:r,reevaluate:!0})},a.onSubtreeChange=function(e,t){a.findAddedMutations(e.addedNodes,t),a.findRemovedMutations(e.removedNodes,e.target,t)},a.findAddedMutations=function(e,t){var n,r,s,i;for(n=0,r=e.length;r>n;n++)s=e[n],1===s.nodeType&&(i=s.nodeName.toUpperCase(),"PICTURE"===i?a.addToElements(s.getElementsByTagName("img")[0],t):"IMG"===i&&o(s,a.selShort)?a.addToElements(s,t):"SOURCE"===i?a.addImgForSource(s,s.parentNode,t):a.addToElements(a.qsa(s,a.selShort),t))},a.findRemovedMutations=function(e,t,n){var r,s,i;for(r=0,s=e.length;s>r;r++)i=e[r],1===i.nodeType&&"SOURCE"===i.nodeName.toUpperCase()&&a.addImgForSource(i,t,n)},a.addImgForSource=function(e,t,n){t&&"PICTURE"!==(t.nodeName||"").toUpperCase()&&((t=t.parentNode)&&"PICTURE"===(t.nodeName||"").toUpperCase()||(t=null)),t&&a.addToElements(t.getElementsByTagName("img")[0],n)},a.addToElements=function(e,t){var n,r;if(e)if("length"in e&&!e.nodeType)for(n=0,r=e.length;r>n;n++)a.addToElements(e[n],t);else e.parentNode&&-1===t.indexOf(e)&&t.push(e)},a.onAttrChange=function(e,t){var n,r=e.target[a.ns];r||"srcset"!==e.attributeName||"IMG"!==(n=e.target.nodeName.toUpperCase())?r&&(n||(n=e.target.nodeName.toUpperCase()),"IMG"===n?(e.attributeName in r&&(r[e.attributeName]=void 0),a.addToElements(e.target,t)):"SOURCE"===n&&a.addImgForSource(e.target,e.target.parentNode,t)):a.addToElements(e.target,t)},a.supPicture||(r&&!a.testMutationEvents?u=new r(a.onMutations):(l=function(){var e=!1,t=[],n=window.setImmediate||window.setTimeout;return function(r){e||(e=!0,l.take||(l.take=function(){t.length&&(a.onMutations(t),t=[]),e=!1}),n(l.take)),t.push(r)}}(),t.documentElement.addEventListener("DOMNodeInserted",function(e){i.connected&&c&&l({type:"childList",addedNodes:[e.target],removedNodes:[]})},!0),t.documentElement.addEventListener("DOMNodeRemoved",function(e){i.connected&&c&&"SOURCE"===(e.target||{}).nodeName&&l({type:"childList",addedNodes:[],removedNodes:[e.target],target:e.target.parentNode})},!0),t.documentElement.addEventListener("DOMAttrModified",function(e){i.connected&&f[e.attrName]&&l({type:"attributes",target:e.target,attributeName:e.attrName})},!0)),window.HTMLImageElement&&Object.defineProperties&&function(){var e=t.createElement("img"),n=[],r=e.getAttribute,s=e.setAttribute,i={src:1};a.supSrcset&&!a.supSizes&&(i.srcset=1),Object.defineProperties(HTMLImageElement.prototype,{getAttribute:{value:function(e){var t;return i[e]&&(t=this[a.ns])&&void 0!==t[e]?t[e]:r.apply(this,arguments)},writeable:!0,enumerable:!0,configurable:!0}}),a.supSrcset||n.push("srcset"),a.supSizes||n.push("sizes"),n.forEach(function(e){Object.defineProperty(HTMLImageElement.prototype,e,{set:function(t){s.call(this,e,t)},get:function(){return r.call(this,e)||""},enumerable:!0,configurable:!0})}),"currentSrc"in e||function(){var e,n=function(e,t){null==t&&(t=e.src||""),Object.defineProperty(e,"pfCurrentSrc",{value:t,writable:!0})},r=n;a.supSrcset&&window.devicePixelRatio&&(e=function(e,t){return(e.d||e.w||e.res)-(t.d||t.w||t.res)},n=function(t){var n,s,i,c,o=t[a.ns];if(o&&o.supported&&o.srcset&&o.sets&&(s=a.parseSet(o.sets[0]))&&s.sort){for(s.sort(e),i=s.length,c=s[i-1],n=0;i>n;n++)if(s[n].d>=window.devicePixelRatio){c=s[n];break}c&&(c=a.makeUrl(c.url))}r(t,c)}),t.addEventListener("load",function(e){"IMG"===e.target.nodeName.toUpperCase()&&n(e.target)},!0),Object.defineProperty(HTMLImageElement.prototype,"currentSrc",{set:function(){window.console&&console.warn&&console.warn("currentSrc can't be set on img element")},get:function(){return this.complete&&n(this),this.src||this.srcset?this.pfCurrentSrc||"":""},enumerable:!0,configurable:!0})}(),!window.HTMLSourceElement||"srcset"in t.createElement("source")||["srcset","sizes"].forEach(function(e){Object.defineProperty(window.HTMLSourceElement.prototype,e,{set:function(t){this.setAttribute(e,t)},get:function(){return this.getAttribute(e)||""},enumerable:!0,configurable:!0})})}(),i.start()),c||t.addEventListener("DOMContentLoaded",function(){c=!0}))}});}if (!("innerHeight"in self&&"innerWidth"in self&&"pageXOffset"in self&&"pageYOffset"in self&&"scrollX"in self&&"scrollY"in self
)) {!function(e){function t(){return(c.scrollLeft||l.scrollLeft||0)-(c.clientLeft||l.clientLeft||0)}function n(){return(c.scrollTop||l.scrollTop||0)-(c.clientTop||l.clientTop||0)}function i(){window.scrollX=window.pageXOffset=t(),window.scrollY=window.pageYOffset=n(),window.innerWidth=c.clientWidth,window.innerHeight=c.clientHeight}var o=e.document,c=o.documentElement,l=o.body||o.createElement("body");try{Object.defineProperties(e,{innerWidth:{get:function(){return c.clientWidth}},innerHeight:{get:function(){return c.clientHeight}},pageXOffset:{get:t},pageYOffset:{get:n},scrollX:{get:t},scrollY:{get:n}})}catch(r){window.attachEvent("onresize",i),window.attachEvent("onscroll",i),i()}}(self);}})('object' === typeof window && window || 'object' === typeof self && self || 'object' === typeof global && global || {});